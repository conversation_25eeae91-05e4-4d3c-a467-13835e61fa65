import { defineConfig } from 'vite'
import dts from "vite-plugin-dts";
import path from "path"

export default defineConfig({
  plugins: [
    dts({
      insertTypesEntry: true,
      tsconfigPath: path.resolve(__dirname, './tsconfig.render.json'),
      outDir: './lib',
      include: ['./src/render'],
      copyDtsFiles: true,
    }),
  ],
  build: {
    lib: {
      entry: {
        render: path.resolve(__dirname, './src/render/index.ts'),
        workers: path.resolve(__dirname, './src/render/workers/index.ts'),
      },
      fileName: (format, entryName) => {
        return `${entryName}.${format}.js`;
      },
      formats: ['es'],
    },
    outDir: './lib',
    rollupOptions: {
      external: [
        'react', 
        'react-dom',
        // 'workerpool'
      ],
      output: {
        globals: {
          'react-dom': 'ReactDOM',
          'react': 'React',
          // 'workerpool': 'workerpool'
        },
      },
    },
  },
})
