{"compilerOptions": {"baseUrl": ".", "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "outDir": "lib", "skipLibCheck": true, "typeRoots": ["node_modules/@types", "./src/render"], "moduleResolution": "bundler", "isolatedModules": true, "noUnusedLocals": false, "noEmit": false, "jsx": "react-jsx", "strict": true, "types": ["react", "react-dom", "node"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "paths": {"react": ["./node_modules/@types/react"]}}, "include": ["src/render/**/*.ts", "src/render/**/*.tsx", "src/render/fabric.d.ts"], "exclude": ["node_modules", "dist"]}