# Roboneo 一期(6.6提测)版本TODO
---

- [x] 基于视口左下角查询适合放置元素的位置
  - `getBoundToLeftBottomByArrangementToPosition`
- [x] 获取画布中所有数据
  - `getSceneData`
- [x] 定位元素至屏幕百分之60（目前80，需支持自定义）
```ts
render.backToOriginPosition({
  target: element,
  scaleFactor: 0.8
})
```
- [x] 支持右键外部元素自动关闭右键菜单
- [x] 支持双击元素定位元素
```ts
  const dbClickElement = (e) => {
    if (render?._FC) {
      showAllObjects(render._FC, [e.target])
    }
  }
```
- [x] 支持文字根据字高（字体大小为屏幕高度的5%）定位元素
- [x] 支持视频元素
  - `createVideo`
- [x] 画布缩放范围0.01 - 4
- [x] 支持图层名称
  - `_shape_name_`
- [x] 支持批量导出和合并导出
- [x] 获取元素在屏幕中的位置
  - `getShapePositionByScreenBound`
- [x] 渲染图片大小
 - 业务渲染
- [x] 主题定制
- [x] 移动至对应层级
 - `render?.Actions.sendZIndex(target, 0);`


 ## Issues

- [ ] 导出的时候ExportElementToImageOptions只支持png和jpeg, 目前我们还希望能导出webp能把这个类型加上不,

- [ ] 拖拽图层后,历史(undo,redo)还存在问题

- [ ] 视频格式的小眼睛切换visible,没有记录到历史

- [x] 切换小手和拖拽样式不生效

- [ ] 视频元素播放按钮
> 是否有必要渲染视频控件