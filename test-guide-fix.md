# 辅助线对比修复测试

## 修复内容
修复了辅助线对比功能只能对比通过校验的图形的问题。

## 修改的文件
- `src/render/plugins/renderGuide/index.ts`

## 修改内容
1. 在 `objectScaling` 和 `objectMoving` 方法中的 `traverse` 函数里添加了过滤逻辑
2. 使用 `isRealElement(root)` 确保只处理真实的业务元素（IMAGE、TEXT、CONTAINER、FRAME、VIDEO）
3. 使用 `(root as any).subTargetCheck === false` 过滤掉未通过校验的图形

## 修改前的问题
- 辅助线会对比所有图形，包括未通过校验的图形
- 这导致用户可能会看到不应该参与对比的图形的辅助线

## 修改后的效果
- 辅助线只会对比通过校验的真实图形元素
- `subTargetCheck: false` 的图形不会参与辅助线对比
- 非真实元素（如遮罩、加载动画等）也不会参与对比

## 测试建议
1. 创建多个图形元素，其中一些设置 `subTargetCheck: false`
2. 移动或缩放图形时，观察辅助线是否只与通过校验的图形对齐
3. 确认未通过校验的图形不会产生辅助线
