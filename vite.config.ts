import { defineConfig } from 'vite'
import path from "path"
import react from '@vitejs/plugin-react-swc'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  base: '/whee/assets/website/canvas-core/',
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  worker: {
    format: 'es'
  },
  server: {
    port: 80,
    host: true,
    cors: true,
    allowedHosts: ['localhost', '127.0.0.1', '0.0.0.0', 'dev.meitu.com'],
  },
})
