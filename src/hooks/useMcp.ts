import { useRenderStore } from "@/store";
import { useEffect } from "react";
type McpOptions = {
    sseUrl: string
}
export function useMcp({ sseUrl }: McpOptions) {
    const { render } = useRenderStore();
    const createSse = () => {
        const sse = new EventSource(sseUrl);
        sse.onmessage = (event) => {
            const data = JSON.parse(event.data);
            console.log(data, "========data")
            
        }
    }
    useEffect(() => {
        createSse()
    }, [])
}