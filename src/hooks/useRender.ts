import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Selection,
  HistoryPlugins,
  ContextMenu,
  RenderStyle,
  FabricObject,
  ElementName,
  TPointerEvent,
  Control,
  InteractiveFabricObject,
  ScaleTheme,
  GuideLine,
} from "@/render";
import { useUiStateStore, useRenderStore } from "@/store";
import { useCallback, useEffect, useState } from "react";
import ContextMenuComponent from "@/components/contextMenu";

export const useRender = (container: string) => {
  const [renderHotKey] = useState<HotKey | null>(null);
  const [renderMouseAction] = useState<MouseAction | null>(null);
  const { setIsCreateFrame, setIsCreateText, setZoom } = useUiStateStore();
  const {
    setRender,
    render,
    setRenderSelection,
    renderSelection,
    setHistoryPlugins,
    historyPlugins,
    setContextMenu,
    setRenderStyle,
  } = useRenderStore();

  const addFrame = useCallback(() => {
    setIsCreateFrame(false);
  }, [setIsCreateFrame]);

  const addText = useCallback(() => {
    setIsCreateText(false);
  }, [setIsCreateText]);
  const handleZoom = useCallback(
    (event: any) => {
      setZoom(Math.floor(event.zoom * 100));
    },
    [setZoom]
  );
  const handleAdd = useCallback(
    ({ target }: { target: FabricObject }) => {
      if (target._name_ === ElementName.FRAME) {
        setIsCreateFrame(false);
      } else if (target._name_ === ElementName.TEXT) {
        setIsCreateText(false);
      }
    },
    [setIsCreateFrame, setIsCreateText]
  );
  useEffect(() => {
    const render = new Render({
      container: container,
      maxImageCount: 1,
      maxZoom: 4,
      minZoom: 0.03,
    });
    setRender(render);
    const selection = new Selection(render, {
      text: {
        text: "Hello",
      },
    });
    const historyPlugins = new HistoryPlugins(render, {
      historyConfig: {
        isDisabledSelect: true,
        max: 100,
        isDisabledMask: true,
      },
    });
    const contextMenuPlugin = new ContextMenu(render, ContextMenuComponent, {});
    const ROTATE_CURSOR =
      "data:image/svg+xml;base64,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";
    const icon =
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE4AAABOCAMAAAC5dNAvAAAAb1BMVEUAAABAj/9Aj/9Aj/9Aj/9Aj/9Aj/9Aj/9Aj/9Aj/9Aj/9Aj/9Aj/9Aj/9Aj/9AkP/3+PpAj//g6/tLlv+bxP3J3vvs8ftuqf5XnP6QvP3V5fuFtv1XnP+nyv2y0fyQvf15sP2+1/tjo/6Qvfx5sP6ticAxAAAAEHRSTlMA30Cf78+QgHBgUCAQv4+/xMK74gAAA4hJREFUWMPlmN12qyAQheNfqqlpGERAjSamff9nPBBiSWUwSs+5Ovuiq2sZP/cwMAPsllUe8uQYRaCUZsckj8tdsOKPCBxlSRzGSsGjaDPxcDRvVremHyklSrSt+0ZMHostsMiwmpqTuerubCyuBcbHB4sYIUQDfF8BKz/uMMbJgii7W3x7SXuPXBgOXGOw0OkUlKwQverJmC/R3rS1nqxUXy0HrIftTMlqUT2CHz5aogPlZIO4Djjxe7uQjfo0/vBxY2SzGD5++eQtxF/hzLcU4EqCJADS+fyLVE55GI6r/EblfOAqSgJFq1k63lX8PQlWr16Pn0O1Axc6fEdLKxTdH2o7NJ0QX83QLoZ7eDbHZghxG80/rIJvyY76Z19kzTkrVarUcEKogJk8QP5kL3PMcbiHzwARw+19j17sjhwFpXqyVl0bxprrGYxu1GMvngpJRxBcBbOe0T66jqT4WttPiajnzQUmXfiPz3xanvtK+oj1TDy4c+3MmrtBQVyZaE2sOK4axpFiFRiYN9pMxerirGqUR5Fo9dQr1TN3IYAVbl2guS13MToSYmmefbqmtW56JucADTKNOsOSsuGoDxiwr+S7xFOaOKXc2x/Q6tjrpnbUvrdqYEh1GQFOOrHOozBRndoUS3o4zpkn4VKo/wsX/c1UpOE4bKFnehqPyCM2bMbVahrji4xWS5szs/zQRYaXgEHXTmTtMykrU7U4wUvAAeCG17sL9n0jX1GLdyXqQ6AlrQOrFpsnpW1kbmeUdKHmD0gmMtUr9mj9ZCiPDuNgBg+1npjGWBFPeZet40GCBycf24DUPEMbFlx+GOQX2+DQRmaj9fBk9+2wZibQ+1+Kx2qi5RjvBkbyS295vqppjrQIjtr97NG/y3JV9Sbt3PlpNp3VjT1EtJvTBDWN8Tz7oQQonM0sBjw/OXs0sPEmWnQza+0tbrWvQnTNQBdKnTVnRk+QX+iqzVnFvz+m/DiV7X93iJqfQUuVDRl6xJMqVOQAKoIHLnUuQIrQ47Fexvk/PLzbdMAlxNvef/Hxte3iQ9iLD9yf3HItI/3ewi+N/MpTHfAag+ZwmhYrLtyAvQRyVmEXbnjAIBl9DYN9iQFQg9D5LyubO+y09n62MECJEPmDBdFht15FBkai6euWmn3T2Dc3w4ITDvMrTiLwKN2HXWwnmcuKFCtYZZwnpyy9c6JTkh9e5PIPacc4riFEQWIAAAAASUVORK5CYII=";
    const renderStyle = new RenderStyle(render, {
      basic: {
        textureSize: 10000,
      },
      rotate: [
        {
          key: "mbm",
          show: true,
          shape: "ellipse",
          xSize: 26,
          ySize: 26,
          offsetX: 0,
          offsetY: 13 + 8,
          backgroundColor: "#FFFFFF",
          icon,
          apply: [ElementName.IMAGE, ElementName.VIDEO, ElementName.CONTAINER],
          cursor: (
            _eventData: TPointerEvent,
            _control: Control,
            fabricObject: InteractiveFabricObject
          ) => `url(${ROTATE_CURSOR}) 16 16, auto`,
        },
      ],
      scale: ScaleTheme,
      frame: {
        maxWidth: 500,
        maxHeight: 500,
        minWidth: 64,
        minHeight: 64,
      },
      selection: {
        defaultSelectionColor: "#F761FF",
        defaultMultiSelectionColor: "#F761FF",
        defaultFrameSelectionColor: "#F761FF",
        defaultImageSelectionColor: "#F761FF",
        defaultTextSelectionColor: "#F761FF",
        defaultContainerSelectionColor: "#F761FF",
      },
    });
    const mouseAction = new MouseAction(render);
    new GuideLine(render._FC);

    setRenderSelection(selection);
    setHistoryPlugins(historyPlugins);
    setContextMenu(contextMenuPlugin);
    setRenderStyle(renderStyle);
    render
      .use(selection)
      .use(historyPlugins)
      .use(renderStyle)
      .use(contextMenuPlugin)
      .use(mouseAction);

    render._FC.on("viewport:zoom", handleZoom);
    render._FC.on("object:added", handleAdd);
    return () => {
      render._FC.off("viewport:zoom", handleZoom);
      render._FC.off("object:added", handleAdd);
      render.unmount();
    };
  }, [
    container,
    addFrame,
    addText,
    setRender,
    setRenderSelection,
    setHistoryPlugins,
    setContextMenu,
    setRenderStyle,
    setZoom,
    handleZoom,
    handleAdd,
  ]);

  return {
    render,
    renderHotKey,
    renderMouseAction,
    renderSelection,
    historyPlugins,
  };
};
