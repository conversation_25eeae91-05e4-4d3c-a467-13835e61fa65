import { useRenderStore } from "@/store";
import { Button } from "../ui/button";
import { ElementName, Group } from "@/render";
import workerpool from 'workerpool'
export default function ValidMask() {
    const u = new URL('../../../lib/works.iife.js', import.meta.url)
    const pool = workerpool.pool(u.toString())
    const { render } = useRenderStore()
    const valid = () => {
        const activeObject = render?._FC.getActiveObject()
        if (activeObject?.get('_name_') === ElementName.IMAGE) {
            const image = activeObject as Group
            const targetShape = image.getObjects().find(item => item.get('maskContainerName') === 'fix_image_mask_container')
            if (!targetShape) {
                console.log('no mask')
            } else {
                const el = targetShape.toCanvasElement()
                const ctx = el.getContext('2d')
                if (!ctx) return
                const imageData = ctx.getImageData(0, 0, el.width, el.height)
                pool.exec('isImageDataHasTransparent', [imageData]).then(res => {
                })
            }
        }
    }
    return (
        <Button onClick={valid}>
            is Mask?
        </Button>
    )
}