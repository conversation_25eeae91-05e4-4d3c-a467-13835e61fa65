import {
    Toolt<PERSON>,
    Toolt<PERSON>Content,
    TooltipProvider,
    TooltipTrigger,
  } from "@/components/ui/tooltip"
export function WrapperTooltip({ children, content }: { children: React.ReactNode, content: string }) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          {children}
        </TooltipTrigger>
        <TooltipContent>
          <p>{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>

  )
}
