import { Button } from '@/components/ui/button'
import { useRenderStore } from "@/store";
import { useCallback, useEffect, useState } from "react";
import { Loader, RefreshCwOff } from 'lucide-react'
import { ElementName, LoadingType, } from "@/render";
import { WrapperTooltip } from '../tooltip';
export default function LoaderElement() {
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isSelected, setIsSelected] = useState<boolean>(false)
  const { render, historyPlugins } = useRenderStore();


  const selectionHandler = useCallback(() => {
    const selected = render?._FC.getActiveObject()
    if (selected?._name_ === ElementName.IMAGE) {
      setIsSelected(true)
    } else {
      setIsSelected(false)
    }
    if (selected?._name_ === ElementName.IMAGE && selected?._loading_) {
      setIsLoading(true)
    } else {
      setIsLoading(false)
    }
    
}, [render])


const setCurrentLoading = (type: LoadingType) => {
  const target = render?._FC.getActiveObject();
  if (!target) return;
  const id = target?._id_;
  const operation = historyPlugins?.baseAction.getLoadOperation([target]);
  if (!operation) return;
  historyPlugins?.submit(operation);
  if (id) {
    // TODO: 设置loading
    render?.Actions.setLoading({
      id,
      type,
      text: 'Generate...',
      blur: true,
      maskOpacity: 0.2,
    });
  }
  setIsLoading(true)
};
const setCurrentLoaded = () => {
  const target = render?._FC.getActiveObject();
  if (!target) return;
  const id = target?._id_;
  const operation = historyPlugins?.baseAction.getLoadOperation([target]);
  if (!operation) return;
  historyPlugins?.submit(operation);
  if (id) {
    render?.Actions.setLoaded(id);
  }
  setIsLoading(false)
};


  useEffect(() => {
      render?._FC.on('selection:created', selectionHandler)
      render?._FC.on('selection:updated', selectionHandler)
      render?._FC.on('selection:cleared', selectionHandler)
      return () => {
          render?._FC.off('selection:created', selectionHandler)
          render?._FC.off('selection:updated', selectionHandler)
          render?._FC.off('selection:cleared', selectionHandler)
      }
  }, [render, selectionHandler])




  const Loaded = (
    <Button
      variant="secondary"
      className="w-fit"
      size="icon"
      onClick={setCurrentLoaded}
      disabled={!isSelected}
    >
      <RefreshCwOff className="w-4 h-4" />
    </Button>
  )

  const Loading = (
    <div className="flex gap-2">
      <Button
      variant="secondary"
      className="w-fit"
      size="icon"
      onClick={() => setCurrentLoading(LoadingType.FADE_IN_TO_OUT)}
      disabled={!isSelected}
    >
      <Loader className="w-4 h-4" /> fadeInToOut
    </Button>
    <Button
      variant="secondary"
      className="w-fit"
      size="icon"
      onClick={() => setCurrentLoading(LoadingType.CIRCLE_DANCE)}
      disabled={!isSelected}
    >
        <Loader className="w-4 h-4" /> circleDance
      </Button>
    </div>
  )



  return (
    <WrapperTooltip content="修改loading状态">
      {/* {isLoading ? Loaded : Loading} */}
      <Button variant="secondary" size="icon" onClick={() => {
        render?.Actions.setLoading({
          id: '1',
          type: LoadingType.FADE_IN_TO_OUT,
          text: 'Generate...',
          blur: true,
          maskOpacity: 0.2,
        })
      }}>
        <Loader className="w-4 h-4" />
      </Button>
    </WrapperTooltip>
  )
}