import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from '@/components/ui/button'
import { useRenderStore } from "@/store";
import { useCallback, useEffect, useState } from "react";
import { LassoSelect } from 'lucide-react'
import { ElementName } from "@/render";
import { WrapperTooltip } from "../tooltip";
export default function Lasso() {
  const [isImage, setIsImage] = useState<boolean>(false)
  const { render } = useRenderStore();

  const selectionHandler = useCallback(() => {
    const selected = render?._FC.getActiveObject()
    if (selected?._name_ === ElementName.IMAGE) {
      setIsImage(true)
    } else {
      setIsImage(false)
    }
}, [render])


  useEffect(() => {
      render?._FC.on('selection:created', selectionHandler)
      render?._FC.on('selection:updated', selectionHandler)
      return () => {
          render?._FC.off('selection:created', selectionHandler)
          render?._FC.off('selection:updated', selectionHandler)
      }
  }, [render, selectionHandler])

  const openLasso = () => {
    // render?.Actions.setLasso({
    //   mode: true,
    // })
  }

  const closeLasso = () => {
    // render?.Actions.setLasso({
    //   mode: false,
    // })
  }



  const DropDown = (
      <DropdownMenu>
          <DropdownMenuTrigger>
              <Button
                  variant="secondary"
                  className="w-fit"
                  size="icon"
              >
                <LassoSelect className="w-4 h-4" />
              </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
              <div className="flex items-center justify-between gap-1">
                  <Button variant="secondary" className="w-fit" size="icon" onClick={openLasso}>开启</Button>
                  <Button variant="secondary" className="w-fit" size="icon" onClick={closeLasso}>关闭</Button>
              </div>
          </DropdownMenuContent>
      </DropdownMenu>
  )

  const DisabledStatus = (
      <Button
          variant="secondary"
          className="w-fit"
          size="icon"
          disabled
      >
        <LassoSelect className="w-4 h-4" />
      </Button>
  )



  return (
    <WrapperTooltip content="改图套索工具">
      {isImage ? DropDown : DisabledStatus}
    </WrapperTooltip>
  )
}