import { Button } from '@/components/ui/button'
import { useRenderStore } from "@/store";
import { useCallback, useEffect, useState } from "react";
import { Eraser, Pencil, OctagonX, FileImage } from 'lucide-react'
import { BrushType, ElementName } from "@/render";
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Slider } from '../ui/slider';
import { cn } from '@/lib/utils';
import { FreeBrush } from '@/render/base/package/freeBrush/brush';
import { WrapperTooltip } from '../tooltip';
export default function Lasso() {
  const [isImage, setIsImage] = useState<boolean>(false)
  const [isEraser, setIsEraser] = useState<boolean>(false)
  const [brushWidth, setBrushWidth] = useState<number>(30)
  const [brushColor] = useState<string>('rgba(53,73,255,1)')
  const [freeBrush, setFreeBrush] = useState<FreeBrush<BrushType.ERASER> | FreeBrush<BrushType.FREE>>()

  const { render } = useRenderStore();

  const selectionHandler = useCallback(() => {
    const selected = render?._FC.getActiveObject()
    if (selected?._name_ === ElementName.IMAGE) {
      setIsImage(true)
    } else {
      setIsImage(false)
    }
  }, [render])


  useEffect(() => {
    render?._FC.on('selection:created', selectionHandler)
    render?._FC.on('selection:updated', selectionHandler)
    return () => {
      render?._FC.off('selection:created', selectionHandler)
      render?._FC.off('selection:updated', selectionHandler)
    }
  }, [render, selectionHandler])

  const initBrush = () => {
    if (freeBrush || !render) return
    const brush = render?.Actions.setDraw({
      width: brushWidth,
      color: brushColor,
    })
    render._FC.selection = false
    setFreeBrush(brush)
    setIsEraser(false)
  }

  const destroyBrush = () => {
    render?.Actions.setDraw({
      mode: false
    })
    setFreeBrush(undefined)
    setIsEraser(false)
  }

  const changeBrush = (erase: boolean) => {
    setIsEraser(erase)
    if (freeBrush) {
      freeBrush.options.isEraser = erase
    }
  }

  useEffect(() => {
    if (freeBrush) {
      freeBrush.setWidth(brushWidth)
    }
  }, [brushWidth, freeBrush])




  const DropDown = (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="secondary"
          className="w-fit"
          size="icon"
          onClick={initBrush}
        >
          <Pencil className="w-4 h-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="w-full flex gap-1">
          <Button variant="secondary" className={cn(!isEraser && 'bg-gray-200')} size="icon" onClick={() => changeBrush(false)}>
            <Pencil className="w-4 h-4" />
          </Button>
          <Button variant="secondary" className={cn(isEraser && 'bg-gray-200')} size="icon" onClick={() => changeBrush(true)}>
            <Eraser className="w-4 h-4" />
          </Button>
          <Slider
            defaultValue={[100]}
            value={[brushWidth]}
            max={100}
            min={10}
            step={1}
            className={cn('w-[90%]', 'mx-auto')}
            onValueChange={(value) => {
              setBrushWidth(value[0]);
            }}
          />
          <Button variant="secondary" className="w-fit" size="icon" onClick={destroyBrush}>
            <OctagonX className="w-4 h-4" />
          </Button>
        </div>
      </PopoverContent>
    </Popover>

  )

  const DisabledStatus = (
    <Button
      variant="secondary"
      className="w-fit"
      size="icon"
      disabled
    >
      <Pencil className="w-4 h-4" />
    </Button>
  )



  return (
    <>
      <Button variant="secondary" size="icon" onClick={() => {
      }}>
        <FileImage />
      </Button>
      <WrapperTooltip content="改图涂抹工具">
        {isImage ? DropDown : DisabledStatus}
      </WrapperTooltip>
    </>
  )
}