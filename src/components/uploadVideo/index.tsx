
import { Upload as ArcoUpload, Message } from '@arco-design/web-react';
import { Button } from '@/components/ui/button';
import { Video } from 'lucide-react';
import file2base64 from '@/utils/file2base64';
import { useRenderStore } from '@/store/render';
import Upload from '@/hooks/uploader';
import { createVideo } from '@/render/utils/shapes';
export default function UploadImage() {
    const { render } =
    useRenderStore();
    const handleVideoChange = async (file: File) => {
        if (render?._FC) {
          const base64 = await file2base64(file);

          const previewImageSrc = await new Promise<string>((resolve, reject) => {
            const el = document.createElement('video');
            el.crossOrigin = 'anonymous';
            el.src = base64;
            el.onloadedmetadata = () => {
                const canvas = document.createElement('canvas');
                canvas.width = el.videoWidth;
                canvas.height = el.videoHeight;
                const ctx = canvas.getContext('2d');
                el.currentTime = 0.1;

                el.onseeked = () => {
                    ctx?.drawImage(el, 0, 0);
                    const blob = canvas.toDataURL('image/png');
                    Upload(blob, 'photo')
                    .then(result => {
                        resolve(result ?? '');
                    })
                    .catch(() => {
                        reject();
                    });
                }
            }

            el.onerror = () => {
                reject();
            }

          });

          const result = await Upload(base64, 'video')
          if (!result || !previewImageSrc) {
            Message.error('上传失败')
            return
          }
          const el = await createVideo('', {
            source: result,
            cover: previewImageSrc,
          });

          render?.addToViewPortCenter(el);
          render?._FC.fire('object:insert', {
            objects: [el],
          });
        }
      };
    return (
        <ArcoUpload
            // autoUpload={false}
            imagePreview={false}
            showUploadList={false}
            customRequest={async ({ file, onSuccess, onError }) => {
                try {
                    await handleVideoChange(file)
                    onSuccess()
                } catch (error) {
                    onError()
                }
            }}
        >
            <Button variant="secondary" size="icon" className="relative">
                <Video className="h-5 w-5" />
            </Button>
        </ArcoUpload>
    )
}