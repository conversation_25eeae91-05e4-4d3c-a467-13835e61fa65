import { ShieldX } from "lucide-react";
import { WrapperTooltip } from "../tooltip";
import { Button } from "../ui/button";
import { useRenderStore } from "@/store";

export default function ErrorStatus() {
  const { render, historyPlugins } = useRenderStore();
  const setCurrentError = () => {
    const target = render?._FC.getActiveObject();
    if (!target) return;
    const id = target?._id_;
    const operation = historyPlugins?.baseAction.getLoadOperation([target]);
    if (!operation) return;
    historyPlugins?.submit(operation);
    if (id) {
      render?.Actions.setError(id, 'error message');
    }
  };
  return (
    <WrapperTooltip content="修改错误状态">
      <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={setCurrentError}
          >
            <ShieldX className="h-5 w-5" />
          </Button>
    </WrapperTooltip>   
  )
}
