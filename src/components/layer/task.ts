import { queryTask, sendTask } from "./utils"
import {Message} from "@arco-design/web-react";

export type TaskOptions = {
    layerId: string
    taskId: string
    status: 'pending' | 'success' | 'error'
    data: any
}
export default class Task {
    tasks: TaskOptions[] = []
    querying: boolean = false
    callback: (data: TaskOptions) => void = () => {}
    errorCallback: (data: TaskOptions) => void = () => {}
    constructor() {
        this.tasks = []
    }

    addTask(layerId: string, src: string, params: {
        poster_translate_flag: string,
        subject_protect_flag: boolean,
        ori_lang: string,
        target_lang: string,
    }) {
        const task = this.tasks.find(task => task.layerId === layerId)
        if (task) {
            return false
        }
        sendTask(src, params)
            .then(res => {
                const taskId = res.data.data.task_id
                this.tasks.push({
                    layerId,
                    taskId,
                    status: 'pending',
                    data: null
                })
                if (!this.querying) {
                    this.queryTaskHandler()
                }
            })
            .catch(err => {
                console.log(err)
                this.errorCallback({
                    layerId,
                    taskId: '',
                    status: 'error',
                    data: null
                })
                Message.warning('任务执行失败')
            })
        return true
    }

    queryTaskHandler() {
        const pendingTasks = this.tasks.filter(task => task.status === 'pending').map(task => task.taskId)
        if (pendingTasks.length === 0 ) {
            this.querying = false
            return
        } else {
            this.querying = true
        }
        const ps = pendingTasks.map(taskId => queryTask(taskId))
        Promise.allSettled(ps)
            .then(res => {
                res.forEach(r => {
                    if (r.status === 'fulfilled') {
                        const { taskId, data } = r.value
                        if (data?.data?.data?.result) {
                            if (data?.data?.data?.result?.code === 0) {
                                const task = this.tasks.find(task => task.taskId === taskId)
                                if (task) {
                                    task.status = 'success'
                                    task.data = data.data.data.result.json_data
                                    this.callback(task)
                                }
                            } else {
                                const task = this.tasks.find(task => task.taskId === taskId)
                                if (task) {
                                    task.status = 'error'
                                    task.data = null
                                    this.errorCallback(task)
                                }
                            }
                        }
                    }
                })
                setTimeout(() => {
                    this.queryTaskHandler()
                }, 2000)
            })
    }

    onTaskSuccess(callback: (data: TaskOptions) => void) {
        this.callback = callback
    }

    onTaskError(callback: (data: TaskOptions) => void) {
        this.errorCallback = callback
    }

}