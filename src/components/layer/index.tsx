import { Layers } from 'lucide-react'
import { Button } from '../ui/button'
import { useRenderStore } from '@/store';
import { useEffect, useState } from 'react';
import { ElementName, getElementOptions, IImage, ImageCustomOptions, ImageOptions, LoadingType } from '@/render';
import Task from './task';
import { Form, Message, Modal, Radio, Switch, Select, Checkbox } from '@arco-design/web-react'
import { useMutation, useQuery } from '@tanstack/react-query';
import { ELIMINATE_TYPE_OPTIONS, ORIGINAL_LANGUAGE_LAYER_OPTIONS, ORIGINAL_LANGUAGE_TRANSLATE_OPTIONS, POSTER_TRANSLATE_FLAG_OPTIONS, TARGET_LANGUAGE_OPTIONS } from './constant';
import { newQueryTask, newSendTask, SendTaskParams } from './api';
import { transformLayerToFrame } from './utils';

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const Option = Select.Option;

const task = new Task()
export default function Layer() {
  const { render } = useRenderStore();
  const [form] = Form.useForm();
  const isTranslate = Form.useWatch('poster_translate_flag', form)
  const [isSelected, setIsSelected] = useState(false)
  const [selectTarget, setSelectTarget] = useState<IImage | null>(null)
  const [modalVisible, setModalVisible] = useState(false)
  const [isSave, setSave] = useState(false)
  const [msgIds, setMsgIds] = useState<{
    shapeId: string,
    msgId: string
  }[]>([])
  const mutation = useMutation({
    mutationFn: ({params, src}: {params: SendTaskParams, src: string, id: string}) => newSendTask(params, src),
    onSuccess: (data, {id}) => {
      setMsgIds(prev => [...prev, {shapeId: id, msgId: data.data.msg_id}])
      render?.Actions.setLoading({
        id: id,
        type: LoadingType.CIRCLE_DANCE,
        text: 'Generate...',
        blur: true,
        maskOpacity: 0.2,
      })
    }
  })
  const query = useQuery({
    queryKey: ['msgIds'],
    queryFn: () => Promise.all(msgIds.map(async ({msgId}) => ({shapeId: msgId, result: await newQueryTask(msgId)}))),
    refetchInterval: 5000,
    refetchIntervalInBackground: true,
  })

  useEffect(() => {
    const success = query.data?.filter(item => item.result.data.error_code === 0)
    if (success?.length){
      success.forEach(({shapeId, result}) => {
        setMsgIds(prev => prev.filter(item => item.shapeId !== shapeId))
        render?.Actions.setLoaded(shapeId)
        const data = result.data?.parameter?.return_json_data?.json_data
        if (data) {
          const frames = transformLayerToFrame(JSON.parse(data))
          frames.forEach(frame => {
            try {
              render?.addFrame(frame)
            } catch (error) {
              Message.error(error instanceof Error ? error.message : '生成失败')
            }
          })
        } else {
          Message.error(result.data.error_msg)
        }
      })
    }
  }, [query.data])

  useEffect(() => {
    setSave(Boolean(localStorage.getItem('is_save_set')))
  }, []);


  const handleSelection = () => {
    const selected = render?._FC.getActiveObject()
    if (selected?._name_ === ElementName.IMAGE) {
      setIsSelected(true)
      setSelectTarget(selected as IImage)
    } else {
      setIsSelected(false)
      setSelectTarget(null)
    }
  }
  const handleTask = async (params: any) => {
    
    if (render?._FC && selectTarget) {
      const { src, _id_ } = getElementOptions.call(render, selectTarget) as (ImageOptions & ImageCustomOptions)
      await mutation.mutateAsync({ params, src, id: _id_ })
    }
  }

  const openModal = () => {
    if (isSave) {
      const preset = localStorage.getItem('poster_preset')
      if (preset) {
        try {
          handleTask(JSON.parse(preset) as any)
          return
        } catch (e) {
          Message.warning('处理参数异常，请重试')
          localStorage.removeItem('poster_preset')
          localStorage.removeItem('is_save_set')
        }
      }
    }
    setModalVisible(true)
  }

  const handleOk = () => {
    const { save, ...values } = form.getFields()
    localStorage.setItem('is_save_set', save)
    setSave(save)
    localStorage.removeItem('poster_preset')
    if (save) {
      localStorage.setItem('poster_preset', JSON.stringify(values))
    }
    handleTask(values as any)
    setModalVisible(false)
  }

  useEffect(() => {
    render?._FC.on('selection:updated', handleSelection)
    render?._FC.on('selection:created', handleSelection)
    render?._FC.on('selection:cleared', handleSelection)
    return () => {
      render?._FC.off('selection:updated', handleSelection)
      render?._FC.off('selection:created', handleSelection)
      render?._FC.off('selection:cleared', handleSelection)
    }
  }, [render])

  if (!isSelected) return null
  return (
    <>
      <Button variant="outline" size="icon" onClick={openModal}>
        <Layers />{isTranslate ? '翻译' : '分层'}
      </Button>
      {
        isSave && <Checkbox checked={isSave} onChange={(checked) => setSave(checked)}>
          应用默认设置
        </Checkbox>
      }
      <Modal
        title='海报分层/翻译选项'
        visible={modalVisible}
        autoFocus={false}
        focusLock={true}
        style={{ width: 670 }}
        onCancel={() => setModalVisible(false)}
        onOk={handleOk}
      >
        <Form
          form={form}
          autoComplete='off'
          initialValues={{
            poster_translate_flag: '1',
            subject_protect_flag: true,
            only_text_eliminate: true,
            eliminate_type: 'big',
            ori_lang: 'ch',
            target_lang: 'ch',
            save: true
          }}
        >
          <FormItem label='分类' field="poster_translate_flag">
            <Select>
              {POSTER_TRANSLATE_FLAG_OPTIONS.map((option) => (
                <Option key={option.key} value={option.key}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </FormItem>

          <FormItem label='海报原语言' field="ori_lang">
            <Select>
              {(isTranslate ? ORIGINAL_LANGUAGE_TRANSLATE_OPTIONS : ORIGINAL_LANGUAGE_LAYER_OPTIONS).map(item => (
                <Option key={item.key} value={item.key}>{item.label}</Option>
              ))}
            </Select>
          </FormItem>
          <FormItem label='海报目标语言' field="target_lang">
            <Select>
              {TARGET_LANGUAGE_OPTIONS.map(item => (
                <Option key={item.key} value={item.key}>{item.label}</Option>
              ))}
            </Select>
          </FormItem>

          <FormItem label='主体保护' field="subject_protect_flag" triggerPropName="checked">
            <Switch />
          </FormItem>
          <FormItem label='仅分层文本' field="only_text_eliminate" triggerPropName="checked">
            <Switch />
          </FormItem>
          <FormItem label='物体消除模型' field="eliminate_type">
            <Select>
              {ELIMINATE_TYPE_OPTIONS.map(item => (
                <Option key={item.key} value={item.key}>{item.label}</Option>
              ))}
            </Select>
          </FormItem>
          <FormItem label='持久化保存' field="save" triggerPropName="checked">
            <Switch />
          </FormItem>
        </Form>
      </Modal>
    </>
  );
}


