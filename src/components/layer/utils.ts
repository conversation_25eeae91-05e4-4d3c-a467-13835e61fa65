import { FrameElementParams, TextElementParams, ImageElementParams } from '@/render'
import { ElementName } from '@/render/utils/enum'
import { nanoid } from 'nanoid'
import axios from 'axios'
export async function sendTask(src: string, params: {
    poster_translate_flag: string,
    subject_protect_flag: boolean,
    ori_lang: string,
    target_lang: string,
}) {
    return await axios.post(
        '//k2-gateway-beta.deepnet.cloud.meitu-int.com/hflow/handle_async/poster-translation-layering-v1/v1',
        {
            "token": "E3474A408C4FA6A2CCB2F31C",
            "image_url": src,
            "ori_lang": params.ori_lang,
            "target_lang": params.target_lang,
            "poster_translate_flag": params.poster_translate_flag,
            "subject_protect_flag": params.subject_protect_flag,
            "app_id": 278
        }
    )
}

export async function queryTask(taskId: string) {
    const result = await axios.post(
        `//k2-gateway-beta.deepnet.cloud.meitu-int.com/hflow/query/v1`,
        {
            "token": "E3474A408C4FA6A2CCB2F31C",
            "task_id": taskId,
            "app_id": 278
        }
    )
    return {
        taskId,
        data: result
    }
}








interface LayerData {
    preview: string
    name: string
    version: string
    templateConf: Array<{
        settingType: null
        width: number
        height: number
        originId: string
        editorColor: string
        threshold: number
        layers: Array<{
            id: string
            layerType: string
            width: number
            height: number
            left: number
            top: number
            rotate: number
            opacity: number
            lock: boolean
            children: any[]
            shine: boolean
            url?: string
            text?: string
            color?: string
            fontSize?: number
            fontFamily?: string
            [key: string]: any
        }>
    }>
    editMode: number
    subject: string
    referenceLine: {
        lines: any[]
        show: boolean
        lock: boolean
    }
    extraInfo: Record<string, any>
}

export const transformLayerToFrame = (data: LayerData): FrameElementParams[] => {
    if (!data || !data.templateConf) {
        return []
    }

    const { templateConf } = data
    return templateConf.map(item => {
        const frameId = nanoid()
        if (!item || !item.layers) {
            return {
                width: 0,
                height: 0,
                backgroundColor: '#fff',
                _name_: ElementName.FRAME,
                children: []
            }
        }

        const children = (item.layers || [])
            .map(layer => {
                if (!layer) {
                    return null
                }

                // 根据图层类型添加特定属性
                switch (layer.layerType) {
                    case 'img':
                        return {
                            ...transformBaseProps(layer),
                            _name_: ElementName.IMAGE,
                            _parent_id_: frameId || '',
                            src: layer.url || '',
                            width: layer.width || 0,
                            height: layer.height || 0,
                            top: -(item.height / 2 - (layer.height || 0) / 2 - (layer.top || 0)),
                            left: -(item.width / 2 - (layer.width || 0) / 2 - (layer.left || 0)),
                            relativelyTop: -(item.height / 2 - (layer.height || 0) / 2 - (layer.top || 0)),
                            relativelyLeft: -(item.width / 2 - (layer.width || 0) / 2 - (layer.left || 0)),
                        } as ImageElementParams
                    case 'bg':
                        return {
                            ...transformBaseProps(layer),
                            _name_: ElementName.IMAGE,
                            _parent_id_: frameId || '',
                            src: layer.url || '',
                            width: layer.width || 0,
                            height: layer.height || 0,
                            top: -(item.height / 2 - (layer.height || 0) / 2 - (layer.top || 0)),
                            left: -(item.width / 2 - (layer.width || 0) / 2 - (layer.left || 0)),
                            relativelyTop: -(item.height / 2 - (layer.height || 0) / 2 - (layer.top || 0)),
                            relativelyLeft: -(item.width / 2 - (layer.width || 0) / 2 - (layer.left || 0)),
                        } as ImageElementParams
                    case 'text':
                        return {
                            ...transformBaseProps(layer),
                            _name_: ElementName.TEXT,
                            _parent_id_: frameId || '',
                            _font_url_: layer?.fontUrl || '',
                            text: layer.text || '',
                            fontFamily: layer.fontFamily,
                            fontSize: layer.fontSize || 16,
                            fontWeight: layer.fontWeight || 'normal',
                            fontStyle: layer.fontStyle || 'normal',
                            linethrough: layer.linethrough || false,
                            underline: layer.underline || false,
                            lineHeight: layer.lineHeight || 1,
                            letterSpacing: layer.letterSpacing || 0,
                            stroke: layer.color || '#000000',
                            strokeWidth: layer.strokeWidth || 1,
                            strokeLineCap: layer.strokeLineCap || 'butt',
                            strokeLineJoin: layer.strokeLineJoin || 'miter',
                            strokeDashArray: layer.strokeDashArray || null,
                            fontUrl: layer?.font_file?.url || '',
                            width: layer.width || 0,
                            height: layer.height || 0,
                            top: -(item.height / 2 - (layer.height || 0) / 2 - (layer.top || 0)),
                            left: -(item.width / 2 - (layer.width || 0) / 2 - (layer.left || 0)),
                            relativelyTop: -(item.height / 2 - (layer.height || 0) / 2 - (layer.top || 0)),
                            relativelyLeft: -(item.width / 2 - (layer.width || 0) / 2 - (layer.left || 0)),
                            fill: layer.color || '#000000',
                            textAlign: layer.textAlign || 'left',
                        } as TextElementParams
                    default:
                        return null
                }
            })
            .filter((item): item is TextElementParams | ImageElementParams => item !== null)

        return {
            width: item.width || 0,
            height: item.height || 0,
            backgroundColor: '#fff',
            _name_: ElementName.FRAME,
            children
        }
    })
}
export const transformBaseProps = (layer: any) => {
    return {
        _id_: layer.id || nanoid(),
        left: layer.left || 0,
        top: layer.top || 0,
        relativelyLeft: layer.left || 0,
        relativelyTop: layer.top || 0,
        scaleX: layer.scaleX || 1,
        scaleY: layer.scaleY || 1,
        flipX: layer.flipX || false,
        flipY: layer.flipY || false,
        angle: layer.rotate || 0,
        _loading_: layer.loading || false,
        _loading_element_: layer.loadingElement || false,
        _message_id_: layer.messageId || '',
        _old_prompt_: layer.oldPrompt || '',
        _new_prompt_: layer.newPrompt || '',
        _parent_id_: layer.parentId || '',
        _name_: layer.layerType === 'img' ? ElementName.IMAGE : layer.layerType === 'text' ? ElementName.TEXT : ElementName.CONTAINER,
        _custom_data_history_: layer.customDataHistory || {},
        _custom_data_: layer.customData || {},
    }
}

export const transformImage = (layer: any): ImageElementParams => {
    return {
        ...transformBaseProps(layer),
        src: layer.url || '',
        width: layer.width || 0,
        height: layer.height || 0,
    }
}

export const transformText = (layer: any): TextElementParams => {
    return {
        ...transformBaseProps(layer),
        text: layer.text || '',
        fontFamily: layer.fontFamily || 'Arial',
        fontSize: layer.fontSize || 16,
        fontWeight: layer.fontWeight || 'normal',
        fontUrl: layer.fontFamily ? `/whee/assets/website/canvas-core/${layer.fontFamily}.ttf` : '',
        width: layer.width || 0,
        height: layer.height || 0,
    }
}

