import axios from "axios";

export enum PosterTranslateFlag {
    'poster_translate' = '1', // 海报翻译
    'poster_layer' = '2', // 海报分层
    'design_poster_layer' = '3', // 设计室海报分层
    'xiu_layer_modify_image' = '4', // 秀秀分层改图
    'RoboNeo_poster_translate' = '5', // RoboNeo海报翻译
}

export enum EliminateType {
    'big' = 'big', // 高精度版本
    'small' = 'small', // 通用版本
}

export type SendTaskParams = {
    image_url: string, // 海报URL
    ori_lang: string, // 原始语言
    target_lang: string, // 目标语言
    poster_translate_flag: PosterTranslateFlag,
    subject_protect_flag: boolean, // 海报翻译阶段是否开启主体保护
    only_text_eliminate: boolean, // 是否只分层文本
    eliminate_type: EliminateType, // 分层类型
}

export function newSendTask(params: SendTaskParams, image_url: string) {
    return axios.post(
        'https://openapi.mtlab.meitu.com/test/poster_trans_rob_async?api_key=7da5fcd48dc84154b1564fe4ab7aabbb&api_secret=b3d3a0f313d545439f64151e88996daa',
        {
            parameter: {
                ...params,
            },
            extra: {},
            media_info_list: [
                {
                    media_data: image_url,
                    media_profiles: {
                        media_data_type: "url"
                    },
                    extra: {}
                },
            ]
        }
    )
}

export function newQueryTask(msg_id: string) {
    return axios.post(
        `https://openapi.mtlab.meitu.com/test/query?api_key=7da5fcd48dc84154b1564fe4ab7aabbb&api_secret=b3d3a0f313d545439f64151e88996daa&msg_id=${msg_id}`,
        {
            msg_id
        }
    )
}