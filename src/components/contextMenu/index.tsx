import {
  createContainerElements,
  ElementName,
  Group,
  newElementOffset,
  Point,
  Canvas,
  getElementOptions,
  isRealElement,
  createElement,
} from '@/render';
import { ContextMenuComponentProps, Operation } from '@/render';
import { DeltaType } from '@/render/plugins/renderHistory/types';
import { useRenderStore } from '@/store';
export default function ContextMenu({
  targets,
  render,
  destroy,
  position,
}: ContextMenuComponentProps) {
  const { historyPlugins } = useRenderStore();

  const isContainer =
    targets?.length === 1 && targets[0]?._name_ === ElementName.CONTAINER;
  return (
    <div className="context-menu w-20 h-fit bg-white rounded-md shadow-md p-2">
      <div className="flex flex-col gap-2">
        <button
          className="w-full h-8 bg-gray-100 rounded-md"
          onClick={() => {
            if (!targets) return;
            const afterPoint = new Point(
              position.x + newElementOffset,
              position.y + newElementOffset
            );
            render.Actions.copyStash(...targets);
            render.Actions.paste(afterPoint);
            destroy();
          }}
        >
          创建副本
        </button>
        <button
          className="w-full h-8 bg-gray-100 rounded-md"
          onClick={() => {
            if (!targets) return;
            render.Actions.copyStash(...targets);
            destroy();
          }}
        >
          复制
        </button>
        <button
          className="w-full h-8 bg-gray-100 rounded-md"
          onClick={async () => {
            if (!targets) return;
            const afterPoint = new Point(position.x, position.y);
            let parent: Group | Canvas = render._FC;
            if (
              targets.length === 1 &&
              targets[0]?._name_ === ElementName.FRAME
            ) {
              parent = targets[0] as Group;
            }
            if (!parent) return;
            const elements = await render.Actions.paste(
              afterPoint,
              true,
              parent
            );
            if (!elements) return;
            const operation = historyPlugins?.baseAction.getAddOperation({
              objects: elements,
            });
            if (!operation) return;
            historyPlugins?.submit(operation);
            destroy();
          }}
        >
          粘贴
        </button>
        <button
          className="w-full h-8 bg-gray-100 rounded-md"
          onClick={() => {
            render.Actions.cut();
            destroy();
          }}
        >
          剪切
        </button>
        <button
          className="w-full h-8 bg-gray-100 rounded-md"
          onClick={() => {
            if (!targets) return;
            const operation = historyPlugins?.baseAction.getRemoveOperation({
              objects: targets,
            });
            if (!operation) return;
            historyPlugins?.submit(operation);
            render.Actions.remove(targets);

            destroy();
          }}
        >
          删除
        </button>
        <button
          className="w-full h-8 bg-gray-100 rounded-md"
          onClick={() => {
            if (!targets) return;
            const operation = historyPlugins?.baseAction.getZIndexOperation(
              targets,
              DeltaType.FRONT
            );
            if (!operation) return;
            historyPlugins?.submit(operation);
            render.Actions.bringToFront(targets);
            destroy();
          }}
        >
          置顶
        </button>
        <button
          className="w-full h-8 bg-gray-100 rounded-md"
          onClick={() => {
            if (!targets) return;
            render.Actions.sendToBack(targets);
            destroy();
          }}
        >
          置底
        </button>
        <button
          className="w-full h-8 bg-gray-100 rounded-md"
          onClick={() => {
            if (!targets) return;
            const operation = historyPlugins?.baseAction.getZIndexOperation(
              targets,
              DeltaType.FORWARD
            );
            if (!operation) return;
            historyPlugins?.submit(operation);
            render.Actions.bringForward(targets);
            destroy();
          }}
        >
          上一层
        </button>
        <button
          className="w-full h-8 bg-gray-100 rounded-md"
          onClick={() => {
            // if (!targets) return;
            let els = targets;
            if (!els) {
              els = render._FC.getActiveObjects();
            }
            const isFrameInEls = els.some(
              (el) => el._name_ === ElementName.FRAME
            );
            if (isFrameInEls) return;
            const operations: Operation[] = [];
            render._FC.discardActiveObject()
            const preObjects = els.map(el => getElementOptions.call(render,el))
            render._FC.remove(...els)
            const container = createContainerElements('', els);
            render._FC.add(container);
            render._FC.setActiveObject(container);
            render._FC.requestRenderAll();
            const afterObjects = [getElementOptions.call(render, container)];
            const operation = new Operation({
              type: DeltaType.MERGE,
              preData: {
                objects: preObjects,
              },
              afterData: {
                objects: afterObjects,
              },
              undoHandler: undefined,
              redoHandler: undefined,
            }, render);
            operations.push(operation);
            historyPlugins?.submit(operations);

            destroy();
          }}
        >
          组合
        </button>
        <button
          disabled={!isContainer}
          className="w-full h-8 bg-gray-100 rounded-md"
          onClick={async () => {
            if (!targets) return;
            const container = targets[0] as Group;
            const preObjects = [getElementOptions.call(render, container)];
            const afterObjects = container.getObjects().filter(el => isRealElement(el)).map(el => getElementOptions.call(render, el));
            const afterShapes = await Promise.all(afterObjects.map(createElement));
            afterShapes.forEach(el => el.set({
              angle: container.angle + el.angle,
            }))
            render._FC.remove(container)
            render._FC.add(...afterShapes)
            const operation = new Operation({
              type: DeltaType.SPLIT,
              preData: {
                objects: preObjects,
              },
              afterData: {
                objects: afterObjects,
              },
              undoHandler: undefined,
              redoHandler: undefined,
            }, render);
            historyPlugins?.submit(operation);
            destroy();
          }}
        >
          拆分
        </button>
      </div>
    </div>
  );
}
