import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from '@/components/ui/button'
import { useRenderStore } from "@/store";
import { AlignMode } from "@/render";
import { commands } from "./constant";
import { useEffect, useState } from "react";
export default function Align() {
    const [isMultiple, setIsMultiple] = useState<boolean>(false)
    const { render, historyPlugins } = useRenderStore();
    const historySet = (mode: AlignMode) => {
        () => {
            const modifyActiveObject = render?._FC.getActiveObject();
            if (!modifyActiveObject) return;
            const beforeData =
                historyPlugins?.baseAction.getElementData({
                    target: modifyActiveObject,
                }) ?? [];
            render?.Actions.alignMode(mode);
            const modifiedActiveObject = render?._FC.getActiveObject();
            if (!modifiedActiveObject) return;
            const afterData =
                historyPlugins?.baseAction.getElementData({
                    target: modifiedActiveObject,
                }) ?? [];
            const operation = historyPlugins?.baseAction.getModifiedOperation({
                beforeData,
                afterData,
            });
            if (!operation) return;
            historyPlugins?.submit(operation);
        }
    }

    useEffect(() => {
        render?._FC.on('selection:created', selectionHandler)
        render?._FC.on('selection:updated', selectionHandler)
        return () => {
            render?._FC.off('selection:created', selectionHandler)
            render?._FC.off('selection:updated', selectionHandler)
        }
    }, [render])



    const selectionHandler = () => {
        const selected = render?._FC.getActiveObjects() ?? []
        setIsMultiple(selected.length > 1)
    }

    const DropDown = (
        <DropdownMenu>
            <DropdownMenuTrigger>
                <Button
                    variant="secondary"
                    className="w-fit"
                    size="icon"
                >对齐</Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
                {
                    commands.map(command => (
                        <DropdownMenuItem key={command.text} onClick={() => historySet(command.mode)}>
                            {command.text}
                        </DropdownMenuItem>
                    ))
                }
            </DropdownMenuContent>
        </DropdownMenu>
    )

    const DisabledStatus = (
        <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            disabled
        >对齐</Button>
    )



    return (
        isMultiple ? DropDown: DisabledStatus
    )
}