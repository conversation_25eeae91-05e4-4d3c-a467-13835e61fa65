import { useRenderStore } from '@/store';
import { Button } from '../ui/button';
import { AlignMode } from '@/render/utils/enum';

export default function Align() {
  const { render } = useRenderStore();
  const setAlignMode = (mode: AlignMode) => {
    render?.Actions.alignMode(mode);
  };

// const historySet = (mode: AlignMode) => {
//   () => {
//     const modifyActiveObject = render?._FC.getActiveObject();
//     if (!modifyActiveObject) return;
//     const beforeData =
//       historyPlugins?.baseAction.getElementData({
//         target: modifyActiveObject,
//       }) ?? [];
//     setAlignMode(mode);
//     const modifiedActiveObject = render?._FC.getActiveObject();
//     if (!modifiedActiveObject) return;
//     const afterData =
//       historyPlugins?.baseAction.getElementData({
//         target: modifiedActiveObject,
//       }) ?? [];
//     const operation = historyPlugins?.baseAction.getModifiedOperation({
//       beforeData,
//       afterData,
//     });
//     if (!operation) return;
//     historyPlugins?.submit(operation);
//   }
// }

  return (
    <div className="flex items-center gap-1 border-x px-3">
      <Button
        variant="secondary"
        className="w-fit"
        size="icon"
        onClick={() => setAlignMode(AlignMode.LEFT)}
      >
        左对齐
      </Button>
      <Button
        variant="secondary"
        className="w-fit"
        size="icon"
        onClick={() => setAlignMode(AlignMode.RIGHT)}
      >
        右对齐
      </Button>
      <Button
        variant="secondary"
        className="w-fit"
        size="icon"
        onClick={() => setAlignMode(AlignMode.CENTER)}
      >
        水平居中
      </Button>
      <Button
        variant="secondary"
        className="w-fit"
        size="icon"
        onClick={() => setAlignMode(AlignMode.TOP)}
      >
        顶对齐
      </Button>
      <Button
        variant="secondary"
        className="w-fit"
        size="icon"
        onClick={() => setAlignMode(AlignMode.BOTTOM)}
      >
        底对齐
      </Button>
      <Button
        variant="secondary"
        className="w-fit"
        size="icon"
        onClick={() => setAlignMode(AlignMode.MIDDLE)}
      >
        垂直居中
      </Button>
    </div>
  );
}
