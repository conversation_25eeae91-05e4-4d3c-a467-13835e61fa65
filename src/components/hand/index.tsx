import { Button } from '@/components/ui/button'
import { useRenderStore } from "@/store";
import { useState } from "react";
import { Hand, MousePointer2 } from 'lucide-react'
import { MoveCanvasOfDefault, RenderCursor } from "@/render";
export default function Lasso() {
  const [isMove, setIsMove] = useState<boolean>(false)
  const { render, renderStyle } = useRenderStore();


  const setMove = () => {
    renderStyle?.setCursorStyle({
      defaults: MoveCanvasOfDefault,
      mousedown: RenderCursor.mousedown,
    });
    setIsMove(true)
    render?.lockAllAndListenMove();
  };
  const setHover = () => {
    renderStyle?.setCursorStyle({
      move: RenderCursor.default,
      hover: RenderCursor.hover,
      defaults: RenderCursor.default,
    });
    setIsMove(false)
    render?.unlockAllAndUnListenMove();
  };



  const Move = (
    <Button
      variant="secondary"
      className="w-fit"
      size="icon"
      onClick={setHover}
    >
      <Hand className="w-4 h-4" />
    </Button>
  )

  const Hover = (
    <Button
      variant="secondary"
      className="w-fit"
      size="icon"
      onClick={setMove}
    >
      <MousePointer2 className="w-4 h-4" />
    </Button>
  )



  return (
    isMove ? Move : Hover
  )
}