import { create } from "zustand";

export const useUiStateStore = create<{
    isCreateFrame: boolean;
    isCreateText: boolean;
    zoom: number;
    setIsCreateFrame: (isCreateFrame: boolean) => void;
    setIsCreateText: (isCreateText: boolean) => void;
    setZoom: (zoom: number) => void;
}>((set) => ({
    isCreateFrame: false,
    isCreateText: false,
    zoom: 100,
    setIsCreateFrame: (isCreateFrame: boolean) => set({ isCreateFrame }),
    setIsCreateText: (isCreateText: boolean) => set({ isCreateText }),
    setZoom: (zoom: number) => set({ zoom }),
}))