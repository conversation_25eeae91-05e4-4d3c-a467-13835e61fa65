import Layout from '@/components/layout'
import Home from './pages/Home'
import { Toaster } from "@/components/ui/toaster"
import "@arco-design/web-react/dist/css/arco.css";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

const queryClient = new QueryClient()

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Layout>
        <Home />
      </Layout>
      <Toaster />
    </QueryClientProvider>
  )
}

export default App
