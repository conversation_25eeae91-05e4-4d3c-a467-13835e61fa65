import { TPointerEvent, TPointerEventInfo, CanvasEvents } from 'fabric';
import { Plugin } from '../../base/types';
import { Render } from '../../render';
import { MouseMove } from './mouseMove';
import { MouseUpBefore } from './mouse-up-down/use-mouse-up-before';
import { MouseDown } from './mouse-up-down/use-mouse-down';

export class MouseAction extends Plugin {
  public __name__ = 'MouseAction';
  private _events: Record<
    string,
    Array<(e: TPointerEventInfo<TPointerEvent>) => void>
  > = {};
  constructor(render: Render) {
    super(render);
    this._events = {
      'mouse:move': [this.handleMouseMove],
      'mouse:up': [this.handleMouseUp],
      'mouse:down': [this.handleMouseDown],
    };
    this.bindEvents();
  }

  public __destroy__ = () => {
    Object.entries(this._events).forEach(([event, handlers]) =>
      handlers.forEach((handler) =>
        this._render._FC.off(event as keyof CanvasEvents, handler)
      )
    );
  };

  private bindEvents(): void {
    Object.entries(this._events).forEach(([event, handlers]) =>
      handlers.forEach((handler) =>
        this._render._FC.on(event as keyof CanvasEvents, handler)
      )
    );
  }

  private handleMouseMove = (e: TPointerEventInfo) => {
    MouseMove(e, this._render._FC);
  };
  private handleMouseUp = (e: TPointerEventInfo) => {
    MouseUpBefore(e);
  };

  private handleMouseDown = (e: TPointerEventInfo) => {
    MouseDown(e);
  };
}
