import type { <PERSON>vas, FabricObject, TPointerEvent } from "fabric";
import { util } from "fabric";

export function resetCurrentTransform(this: <PERSON><PERSON>, e: TPointerEvent, target: FabricObject) {
    if (!this._currentTransform) return;
    const pointer = target.group
        ? util.sendPointToPlane(this.getScenePoint(e), undefined, target.group.calcTransformMatrix())
        : this.getScenePoint(e);

    Object.assign(this._currentTransform, {
        offsetX: pointer.x - target.left,
        offsetY: pointer.y - target.top,
        ex: pointer.x,
        ey: pointer.y,
        lastX: pointer.x,
        lastY: pointer.y,
    });
    Object.assign(this._currentTransform.original, {
        left: target.left,
        top: target.top,
    });
}
