import { DefaultFontPreset, InFrameFontPreset } from "../../../../base/package/preset";
import { Frame } from "../../../../base/package/frame/frame";
import { ElementName } from "../../../../utils";
import { FabricObject, Group } from "fabric";

export const changeFontStyle = (item: FabricObject, targetParent: Frame | undefined) => {
    const name = item.get('_name_')
    if (name === ElementName.TEXT) {
        if (!targetParent) {
            item.set({
                ...DefaultFontPreset,
            })
        } else {
            item.set({
                ...InFrameFontPreset,
            })
        }
    }
}

export const changeStyle = (item: FabricObject, targetParent: Frame | undefined) => {
    const name = item.get('_name_')
    if (name === ElementName.CONTAINER) {
        const els = (item as Group).getObjects()
        els.forEach(el => {
            changeStyle(el, targetParent)
        })
    }
    if (name === ElementName.TEXT) {
        changeFontStyle(item, targetParent)
    }
}
