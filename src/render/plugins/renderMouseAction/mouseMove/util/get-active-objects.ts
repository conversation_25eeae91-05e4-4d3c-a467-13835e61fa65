import { ActiveSelection, type FabricObject } from "fabric";

function isEffectObject(obj: FabricObject) {
    if (!obj.evented) return false;
    if (!obj.selectable) return false;
    const isLock = obj.lockMovementX && obj.lockMovementY && obj.lockRotation;
    if (isLock) return false;
    return true;
}

export function getActiveObjects(target: FabricObject) {
    const list = target instanceof ActiveSelection ? target.getObjects() : [target];
    return list.filter((x) => isEffectObject(x));
}
