import { Canvas, FabricObject, Group, Point, TPointerEventInfo } from "fabric";
import { Frame } from "../../../../base/package/frame/frame";

function getFrames(canvas: Canvas, point: Point) {
    const frames: Frame[] = [];
    const exclude: Frame[] = [];
    const step = (list: FabricObject[]) => {
        for (const item of list) {
            if (!item.evented) continue;
            if (!item.visible) continue;
            if (!item.selectable) continue;
            if (!(item instanceof Group)) continue;
            const f = item instanceof Frame;
            if (!item.containsPoint(point)) {
                if (!f) continue;
                if (item.clip) continue;
            } else {
                if (!f) return;
                frames.push(item)
            }
        }
    };
    const selectedObjects = canvas.getActiveObjects();
    step(canvas.getObjects().reverse().filter(item => !selectedObjects.includes(item)));

    const filter = (list: FabricObject[]) => {
        for (const item of list) {
            const f = item instanceof Frame;
            if (f) exclude.push(item);
            if (item instanceof Group) filter(item.getObjects());
        }
    };
    filter(canvas.getActiveObjects());
    const allObjects = canvas.getObjects();
    frames.sort((a, b) => allObjects.indexOf(b) - allObjects.indexOf(a));

    return { exclude, frames };
}

export function getParentFrame(canvas: Canvas, e: TPointerEventInfo) {
    const { scenePoint, target } = e;
    if (!target) return;
    const { frames, exclude } = getFrames(canvas, scenePoint);
    for (const item of exclude) {
        const fIndex = frames.indexOf(item);
        if (fIndex == -1) continue;
        frames.splice(fIndex, 1);
    }

    const dim = target._getTransformedDimensions();
    for (let i = 0; i < frames.length; i++) {
        let p = frames[i].parent;
        while (p && p.constructor == Group) p = p.parent;
        if (!p) continue;
        const d = frames[i]._getTransformedDimensions();
        if (dim.x > d.x || dim.y > d.y) {
            frames.splice(i--, 1);
            continue;
        }
    }

    return frames[0];
}

