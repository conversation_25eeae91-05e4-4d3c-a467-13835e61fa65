import { ActiveSelection, Canvas, type FabricObject, type TPointerEventInfo } from "fabric";
import { mouse } from "../mouse-up-down/util";
import {
    canLeaveParent,
    getActiveObjects,
    getParentFrame,
    resetCurrentTransform,
} from "./util";
import { changeStyle } from "./util/style";

export const MouseMove = (e: TPointerEventInfo, canvas: Canvas) => {
    if (!mouse.isDown) return;
    if (!canvas.selection) return;
    if (e.transform?.action != "drag") return;
    const target = e.target;
    if (!target) return;
    if (target.type === 'frame') return
    if (!target.selectable) return;
    const isA = target instanceof ActiveSelection;
    const list = getActiveObjects(target);
    const isFrameIn = list.find(item => item.type === 'frame')
    if (isFrameIn) return;
    if (!list.length) return;

    const targetParent = getParentFrame(canvas, e);
    const fn = (item: FabricObject) => {
        (item.parent ?? canvas).remove(item);
        (targetParent ?? canvas).add(item);
        changeStyle(item, targetParent)
        item.set({
            _parent_id_: targetParent?._id_ ?? '',
        })
    };
    const step = (item: FabricObject) => {
        const b = canLeaveParent(item, targetParent);
        if (!b) return;

        if (isA) {
            target.remove(item);
            fn(item);
            target.add(item);
        } else {
            canvas._activeObject = undefined;
            canvas._hoveredTarget = undefined;
            fn(target);
            canvas._activeObject = target;
            canvas._hoveredTarget = target;
            resetCurrentTransform.call(canvas, e.e, target);
        }
    };

    for (const item of list) step(item);
}
