import { RenderCursor } from "../../base/ownDefaults";
import { ISelectionOptions } from "./types";


export const defaultOptions: ISelectionOptions = {
    color: '#FFFFFF',
    opacity: 0.5,
    text: {
      fontSize: 20,
      fontFamily: 'Arial',
      fill: '#000000',
      text: 'Hello, Infinite Canvas',
    },
    cursor: {
        CreateText: RenderCursor.insertText,
        TextEdit: RenderCursor.insertText,
        CreateFrame: RenderCursor.insertFrame,
        defaults: RenderCursor.default,
    }
  };