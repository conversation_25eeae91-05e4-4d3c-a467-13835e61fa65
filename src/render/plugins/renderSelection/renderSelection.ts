import { Group, Point, TPointerEvent, TPointerEventInfo } from 'fabric';
import { CoreMouseMode, ElementName } from '../../utils/enum';
import { Plugin } from '../../base/types';
import { Render } from '../../render';
import { createFrame, createText } from '../../utils/shapes';
import { ISelectionOptions, TStartPoint } from './types';
import { scenePointInObject } from '../../utils/canvas';
import { defaultOptions } from './constant';
import { DefaultFontPreset, FramePreset, InFrameFontPreset } from '../../base/package/preset'
import { FramePresetType } from '../../base/package/preset/frame.type';

/**
 * 选择工具类,用于处理画布上的选择、创建框架和文本等操作
 */
export class Selection extends Plugin {
  public __name__ = 'MouseSelection';
  private _Mode: CoreMouseMode = CoreMouseMode.SELECTION;
  private _startPoint: TStartPoint = { x: 0, y: 0 };
  private _isMouseDown = false;
  private _scenePoint: {
    start: Point;
    width: number;
    height: number;
  } = {
    start: new Point(0, 0),
    width: 0,
    height: 0,
  };
  private _framePreset: FramePresetType = {
    maxHeight: 1024,
    maxWidth: 1024,
    minHeight: 64,
    minWidth: 64,
  }

  opt: Required<ISelectionOptions> = defaultOptions;

  /**
   * 创建Selection实例
   * @param _Core - Core实例
   * @param _FC - Fabric Canvas实例
   */
  constructor(render: Render, public options?: Partial<ISelectionOptions>) {
    super(render);
    this.opt = {
      ...defaultOptions,
      ...(options ?? {}),
    };
    this._addEvent();
  }

  /**
   * 设置当前的操作模式
   * @param mode - 要设置的模式
   */
  public setMode = (mode: CoreMouseMode) => {
    this._Mode = mode;
    if (mode === CoreMouseMode.FRAME || mode === CoreMouseMode.TEXT) {
      this._render._FC.selection = false;
      this._render._lockAll();
    } else {
      this._render._FC.selection = true;
      this._render._unlockAll();
    }
    this._render._FC.renderAll();
  };
  /**
   * 销毁插件
   */
  public __destroy__ = () => {
    this._render._FC.off('mouse:down', this._mouseDown);
    this._render._FC.off('mouse:move', this._mouseMove);
    this._render._FC.off('mouse:up', this._frameMouseUp);
    this._render._FC.off('mouse:up', this._textMouseUp);
  };

  /**
   * 添加画布事件监听器
   * @private
   */
  private _addEvent = () => {
    this._render._FC.on('mouse:down', this._mouseDown);
    this._render._FC.on('mouse:move', this._mouseMove);
    this._render._FC.on('mouse:up', this._frameMouseUp);
    this._render._FC.on('mouse:up', this._textMouseUp);
  };

  /**
   * 处理鼠标按下事件
   * @param e - 鼠标事件信息
   * @private
   */
  private _mouseDown = (e: TPointerEventInfo<TPointerEvent>) => {
    if (this._Mode !== CoreMouseMode.FRAME) return;
    this._isMouseDown = true;
    this._scenePoint.start = this._render._FC.getScenePoint(e.e);
    const pointer = this._render._FC
      .getScenePoint(e.e)
      .transform(this._render._FC.viewportTransform);
    this._startPoint = {
      x: pointer.x,
      y: pointer.y,
    };
    this._render._FC.renderAll();
  };

  /**
   * 处理鼠标移动事件
   * @param e - 鼠标事件信息
   * @private
   */
  private _mouseMove = (e: TPointerEventInfo<TPointerEvent>) => {
    if (this._Mode !== CoreMouseMode.FRAME) return;
    if (!this._isMouseDown) return;

    this._scenePoint.width =
      this._render._FC.getScenePoint(e.e).x - this._scenePoint.start.x;
    this._scenePoint.height =
      this._render._FC.getScenePoint(e.e).y - this._scenePoint.start.y;
    const pointer = this._render._FC
      .getScenePoint(e.e)
      .transform(this._render._FC.viewportTransform);
    const width = pointer.x - this._startPoint.x;
    const height = pointer.y - this._startPoint.y;

    this.draw(this._startPoint.x, this._startPoint.y, width, height);
    this._render._FC.renderAll();
  };

  draw = (x: number, y: number, width: number, height: number) => {
    const ctx = this._render._FC.contextTop;
    const maxWidth = FramePreset.maxWidth * this._render._FC.getZoom()
    const maxHeight = FramePreset.maxHeight * this._render._FC.getZoom()
    const minWidth = FramePreset.minWidth * this._render._FC.getZoom()
    const minHeight = FramePreset.minHeight * this._render._FC.getZoom()
    if (
      Math.abs(width) < minWidth && Math.abs(height) < minHeight ||
      Math.abs(width) > maxWidth && Math.abs(height) > maxHeight
    ) return
    this._render._FC.clearContext(ctx);
    const flipX = width > 0 ? 1 : -1
    const flipY = height > 0 ? 1 : -1
    const renderWidth = Math.min(Math.max(Math.abs(width), minWidth), maxWidth)
    const renderHeight = Math.min(Math.max(Math.abs(height), minHeight), maxHeight)
    // const renderWidth = Math.abs(width)
    // const renderHeight = Math.abs(height)
    ctx.save();
    ctx.beginPath();
    ctx.rect(x, y, renderWidth * flipX, renderHeight * flipY);
    ctx.fillStyle = this.opt.color;
    ctx.globalAlpha = this.opt.opacity;
    ctx.fill();
    ctx.restore();
  };

  /**
   * 处理框架创建时的鼠标抬起事件
   * @param e - 鼠标事件信息
   * @private
   */
  private _frameMouseUp = (_e: TPointerEventInfo<TPointerEvent>) => {
    const ctx = this._render._FC.contextTop;
    if (this._Mode !== CoreMouseMode.FRAME) return;
    if (!this._isMouseDown) return;
    this._render._FC.clearContext(ctx);
    const { width, height, start } = this._scenePoint;
    let targetWidth = 0
    let targetHeight = 0
    if (width === 0 && height === 0) {
      targetWidth = this._framePreset.maxWidth
      targetHeight = this._framePreset.maxHeight
    } else {
      targetWidth = Math.min(Math.max(Math.abs(width), this._framePreset.minWidth), this._framePreset.maxWidth)
      targetHeight = Math.min(Math.max(Math.abs(height), this._framePreset.minHeight), this._framePreset.maxHeight)
    }

    this._render.logger.info(`targetWidth: ${targetWidth}, targetWidth: ${targetWidth}`)
    const frame = createFrame('', {
      subTargetCheck: true,
      interactive: true,
      backgroundColor: this.opt.color,
      width: targetWidth,
      height: targetHeight,
      left: start.x + (targetWidth > 10 ? targetWidth : 10) / 2,
      top: start.y + (targetHeight > 10 ? targetHeight : 10) / 2,
    });
    this._render.add(frame);
    this._render._FC.fire('object:insert', {
      objects: [frame],
    });
    this._render._FC.setActiveObject(frame);
    this._render._FC.renderAll();
    this.setMode(CoreMouseMode.SELECTION);
    this._isMouseDown = false;
  };

  /**
   * 处理文本创建时的鼠标抬起事件
   * @param e - 鼠标事件信息
   * @private
   */
  private _textMouseUp = (e: TPointerEventInfo<TPointerEvent>) => {
    if (this._Mode !== CoreMouseMode.TEXT) return;
    const text = createText('', {
      ...this.opt.text,
      left: e.scenePoint.x,
      top: e.scenePoint.y,
    });
    text.set({
      left: e.scenePoint.x + text.width! / 2,
      top: e.scenePoint.y + text.height! / 2,
    });
    const target = scenePointInObject(this._render._FC, e.scenePoint);
    if (target.get('_name_') === ElementName.FRAME) {
      text.set('_parent_id_', target.get('_id_'));
      (target as Group).add(text);
      text.set({
        ...InFrameFontPreset
      })
    } else {
      text.set({
        ...DefaultFontPreset
      })

      this._render.add(text);
    }
    this._render._FC.fire('object:insert', {
      objects: [text],
    });
    text.enterEditing();
    this.setMode(CoreMouseMode.SELECTION);
  };
}
