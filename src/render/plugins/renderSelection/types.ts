import { ITextProps } from "fabric";

/**
 * 表示起始点坐标的类型
 */
export type TStartPoint = {
    x: number;
    y: number;
  };
  
export type TCursorOptions = {
    CreateText: string,
    TextEdit: string,
    CreateFrame: string,
    defaults: string,
}

/**
 * 选择选项
 */
export interface ISelectionOptions {
    color: string,
    opacity: number,
    text: Partial<ITextProps & {
        text: string,
    }>,
    cursor: Partial<TCursorOptions>
  }
  