import { z } from "zod";
import { Render } from "../../render";
import { Rect } from "fabric";
import { getViewPortVisibleArea } from "../../utils";
export const scheme = z.object({
    maskColor: z.string().optional().default('#000000'),
    maskOpacity: z.number().optional().default(0.5),
})



/**
 * 渲染遮罩
 * @param this 
 * @param options 
 */
export function renderMask(this: Render, options: z.infer<typeof scheme>) {
    const viewportArea = getViewPortVisibleArea(this._FC);
    const width = viewportArea.right - viewportArea.left;
    const height = viewportArea.bottom - viewportArea.top;
    const maskElement = new Rect({
      width,
      height,
      scaleX: 1,
      scaleY: 1,
      left: viewportArea.left + width / 2,
      top: viewportArea.top + height / 2,
      fill: options.maskColor,
      opacity: options.maskOpacity,
      selectable: false,
      evented: false,
    });
    this._FC._objects.push(maskElement);
    this._FC.renderAll();
    return maskElement;
}

/**
 * 视口平移时，保持遮罩层固定在画布中心，不随视口平移
 * @param this 
 * @param render 
 */
export function viewportTranslateHandler(this: Rect, render: Render) {
    const viewportArea = getViewPortVisibleArea(render._FC);
    const width = viewportArea.right - viewportArea.left;
    const height = viewportArea.bottom - viewportArea.top;
    this.set({
      width,
      height,
      left: viewportArea.left + width / 2,
      top: viewportArea.top + height / 2,
    });
    render._FC.renderAll();
  }

/**
 * 视口缩放时，保持遮罩层缩放比例与视口一致
 * @param this 
 * @param render 
 */
export function viewportZoomHandler(this: Rect, render: Render) {
    const viewportArea = getViewPortVisibleArea(render._FC);
    const width = viewportArea.right - viewportArea.left;
    const height = viewportArea.bottom - viewportArea.top;
    this.set({
      width,
      height,
      left: viewportArea.left + width / 2,
      top: viewportArea.top + height / 2,
    });
    render._FC.renderAll();
  }