import { FixedLayout, Group, LayoutManager } from "fabric";
import { Render } from "../../render";
import { z } from "zod";

const extendRectOptionsSchema = z.object({
    width: z.number(),
    height: z.number(),
    extendLeft: z.number(),
    extendTop: z.number(),
    minExtendWidth: z.number().optional().default(1),
    maxExtendWidth: z.number(),
    minExtendHeight: z.number().optional().default(1),
    maxExtendHeight: z.number(),
    // allowImageOutOfExtend: z.boolean().optional().default(false),
    extendFill: z.string().optional().default('#000'),
    extendOpacity: z.number().optional().default(0.5),
})

export function createExtendRect(this: Render, options: z.infer<typeof extendRectOptionsSchema>) {
    const result = extendRectOptionsSchema.parse(options)
    const extendGroup = new Group([], {
        layoutManager: new LayoutManager(new FixedLayout()),
        width: result.width,
        height: result.height,
        left: result.extendLeft,
        top: result.extendTop,
        backgroundColor: result.extendFill,
        opacity: result.extendOpacity,
    })

    extendGroup.set('maxExtendWidth', result.maxExtendWidth)
    extendGroup.set('minExtendWidth', result.minExtendWidth)
    extendGroup.set('maxExtendHeight', result.maxExtendHeight)
    extendGroup.set('minExtendHeight', result.minExtendHeight)
    // extendGroup.set('allowImageOutOfExtend', result.allowImageOutOfExtend)
    extendGroup.setControlVisible("mt", true);
    extendGroup.setControlVisible("mt", true);
    extendGroup.setControlVisible("mb", true);
    extendGroup.setControlVisible("mb", true);
    extendGroup.set("_name_", 'extendRect');
    this._FC.discardActiveObject();
    this._FC.set('selection', false);

    this._FC.add(extendGroup)
    return extendGroup
}