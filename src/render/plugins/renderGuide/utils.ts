import { FabricObject, Point } from "fabric";

type VerticalLineCoords = {
  x: number;
  y1: number;
  y2: number;
};

type HorizontalLineCoords = {
  y: number;
  x1: number;
  x2: number;
};

export const getPositions = (corner?: string) => {
  if (corner === 'br') return ['br'];
  if (corner === 'bl') return ['bl'];
  if (corner === 'tr') return ['tr'];
  if (corner === 'tl') return ['tl'];
  return ['br', 'bl', 'tr', 'tl', 'c'];
}


export const cornerMapping: Record<string, (target: FabricObject) => {
  originX: string
  originY: string
  left: number
  top: number
  originHorizontal: Point
  originVertical: Point
  anchorPoint: Point
}> = {
  'tr': (target) => {
    const {
       bl: { x: blX, y: blY },
       br,
    } = target.aCoords
    return {
      originX: 'left',
      originY: 'bottom',
      left: blX,
      top: blY,
      originHorizontal: new Point(blX, blY),
      originVertical: new Point(br.x, br.y),
      anchorPoint: new Point(blX, blY),
    }
  },
  'tl': (target) => {
    const {
       br: { x: brX, y: brY },
       tr,
       bl
    } = target.aCoords
    return {
      originX: 'right',
      originY: 'bottom',
      left: brX,
      top: brY,
      originHorizontal: new Point(tr.x, tr.y),
      originVertical: new Point(bl.x, bl.y),
      anchorPoint: new Point(brX, brY),
    }
  },
  'bl': (target) => {
    const { 
      tr: { x: trX, y: trY },
      br,
      tl
    } = target.aCoords
    return {
      originX: 'right',
      originY: 'top',
      left: trX,
      top: trY,
      originHorizontal: br,
      originVertical: tl,
      anchorPoint: new Point(trX, trY),
    }
  },
  'br': (target) => {
    const {
       tl: { x: tlX, y: tlY },
       tr,
       bl
    } = target.aCoords
    return {
      originX: 'left',
      originY: 'top',
      left: tlX,
      top: tlY,
      originHorizontal: bl,
      originVertical: tr,
      anchorPoint: new Point(tlX, tlY),
    }
  },
}


export function getSnapScaleFromNearestLines({
  dragPoint,
  anchorPoint,
  verticalLines,
  horizontalLines,
  scaleX,
  scaleY,
}: {
  dragPoint: { x: number; y: number };
  anchorPoint: { x: number; y: number };
  verticalLines: VerticalLineCoords[];
  horizontalLines: HorizontalLineCoords[];
  scaleX: number;
  scaleY: number;
}): number | undefined {
  const { x: x1, y: y1 } = dragPoint;
  const { x: x2, y: y2 } = anchorPoint;

  // 最近辅助线
  const nearestX = verticalLines.length > 0
    ? verticalLines.reduce((prev, curr) =>
        Math.abs(curr.x - x1) < Math.abs(prev.x - x1) ? curr : prev
      ).x
    : undefined;

  const nearestY = horizontalLines.length > 0
    ? horizontalLines.reduce((prev, curr) =>
        Math.abs(curr.y - y1) < Math.abs(prev.y - y1) ? curr : prev
      ).y
    : undefined;

  const scale: { scaleX?: number; scaleY?: number } = {};

  if (nearestX !== undefined && x1 !== x2) {
    const deltaOriginal = x1 - x2;
    const deltaSnap = nearestX - x2;
    const sign = Math.sign(deltaOriginal)
    const distanceRatio = Math.abs(deltaSnap) / Math.abs(deltaOriginal) * scaleX;
    scale.scaleX = distanceRatio * sign;
  }

  if (nearestY !== undefined && y1 !== y2) {
    const deltaOriginal = y1 - y2;
    const deltaSnap = nearestY - y2;
    const sign = Math.sign(deltaOriginal);
    const distanceRatio = Math.abs(deltaSnap) / Math.abs(deltaOriginal) * scaleY;
    scale.scaleY = distanceRatio * sign;
  }

  if (scale.scaleX && scale.scaleY) {
    return Math.min(scale.scaleX, scale.scaleY);
  }
  return scale.scaleX || scale.scaleY;
}