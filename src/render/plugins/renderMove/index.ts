import { Plugin } from "../../base/types";
import { Ren<PERSON> } from "../../render";
import { ElementName } from "../../utils";

export class MoveAction extends Plugin {
    public __name__ = 'MoveToFrame'
    private isDown: boolean = false
    constructor(private render: Render) {
        super(render)
        this.bindEvent()
    }
    
    private bindEvent = () => {
        this.render._FC.on('mouse:down', this.mouseDownHandler)
        this.render._FC.on('mouse:move', this.mouseMoveHandler)
        this.render._FC.on('mouse:up', this.mouseUpHandler)
    }

    public __destroy__ = () => {
        this.render._FC.off('mouse:down', this.mouseDownHandler)
        this.render._FC.off('mouse:move', this.mouseMoveHandler)
        this.render._FC.off('mouse:up', this.mouseUpHandler)
    }

    private mouseDownHandler = () => {
        this.isDown = true
    }
    private mouseMoveHandler = () => {
        if (!this.isDown) return 
        const objs = this.render._FC.getActiveObjects()
        const constants = objs.some(item => item._name_ === ElementName.FRAME)
        if (constants) return
        
    }
    private mouseUpHandler = () => {
        this.isDown = false
    }
}
