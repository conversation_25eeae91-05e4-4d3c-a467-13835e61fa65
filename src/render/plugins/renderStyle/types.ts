import { InteractiveFabricObject } from "fabric";

import { Control } from "fabric";

import { TPointerEvent } from "fabric";
/**
 * 鼠标样式处理函数
 * @param eventData 事件数据
 * @param control 控制
 * @param fabricObject 交互对象
 * @returns 鼠标样式
 */
export type CursorStyleHandler = (
    eventData: TPointerEvent,
    control: Control,
    fabricObject: InteractiveFabricObject,
  ) => string;

export type CursorStyleHandlerByString = (
    angle: number,
  ) => string;

// 旋转鼠标样式
export type RotateCursorStyle = CursorStyleHandler | string | {
    mtl: CursorStyleHandler | string;
    mtr: CursorStyleHandler | string;
    mbl: CursorStyleHandler | string;
    mbr: CursorStyleHandler | string;
}

/**
 * 旋转鼠标样式获取SVG
 */
export type RotateHandlerCursorByString = {
    mtlHandler: CursorStyleHandlerByString;
    mtrHandler: CursorStyleHandlerByString;
    mblHandler: CursorStyleHandlerByString;
    mbrHandler: CursorStyleHandlerByString;
    mtmHandler: CursorStyleHandlerByString;
    mbmHandler: CursorStyleHandlerByString;
    mlmHandler: CursorStyleHandlerByString;
    mrmHandler: CursorStyleHandlerByString;
}
/**
 * 缩放鼠标样式获取SVG
 */
export type ScaleHandlerCursorByString = {
    blHandler: CursorStyleHandlerByString;
    brHandler: CursorStyleHandlerByString;
    tlHandler: CursorStyleHandlerByString;
    trHandler: CursorStyleHandlerByString;
}

/**
 * 画布鼠标样式
 */
export interface CanvasMouseStyle {
    defaults: string;
    hover: string;
    move: string;
}


/**
 * 控制器鼠标样式
 */
export interface ActionMouseStyle {
    rotateHandler: RotateHandlerCursorByString;
    scaleHandler: ScaleHandlerCursorByString;
}

export type MouseStyle = CanvasMouseStyle & ActionMouseStyle & {
    mousedown: string;
}

export type MouseStyleOptions = CanvasMouseStyle & {
    mousedown?: string;
}

export type BaseControlsCursorParams = {
    tr: CursorStyleHandler;
    tl: CursorStyleHandler;
    bl: CursorStyleHandler;
    br: CursorStyleHandler;
    mtl: CursorStyleHandler;
    mtr: CursorStyleHandler;
    mbl: CursorStyleHandler;
    mbr: CursorStyleHandler;
}