import { ownDefaultsMouseStyle } from "../../../../base/ownDefaults";
import { svgToBase64 } from "../../../../utils";
import { InteractiveFabricObject } from "fabric"
import { z } from "zod/v4";

import { Control } from "fabric"
import { scaleRenderControlsSchema } from "../../schema/control";

import { TPointerEvent } from "fabric";

export const ScaleTheme: z.infer<typeof scaleRenderControlsSchema> = [
    {
        key: 'tl',
        cursor: (
          _eventData: TPointerEvent,
          _control: Control,
          fabricObject: InteractiveFabricObject,
      ) => `url(${svgToBase64(ownDefaultsMouseStyle.scaleHandler.tlHandler(fabricObject.angle))}) 16 16, auto`,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        backgroundColor: '#fff',
        icon: '',
        apply: ['image', 'text', 'container', 'video', 'extendRect'],
      },
      {
        key: 'tr',
        cursor: (
          _eventData: TPointerEvent,
          _control: Control,
          fabricObject: InteractiveFabricObject,
      ) => `url(${svgToBase64(ownDefaultsMouseStyle.scaleHandler.trHandler(fabricObject.angle))}) 16 16, auto`,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        backgroundColor: '#fff',
        icon: '',
        apply: ['image', 'text', 'container', 'video', 'extendRect'],
      },
      {
        key: 'bl',
        cursor: (
          _eventData: TPointerEvent,
          _control: Control,
          fabricObject: InteractiveFabricObject,
      ) => `url(${svgToBase64(ownDefaultsMouseStyle.scaleHandler.blHandler(fabricObject.angle))}) 16 16, auto`,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        backgroundColor: '#fff',
        icon: '',
        apply: ['image', 'text', 'container', 'video', 'extendRect'],
      },
      {
        key: 'br',
        cursor: (
          _eventData: TPointerEvent,
          _control: Control,
          fabricObject: InteractiveFabricObject,
      ) => `url(${svgToBase64(ownDefaultsMouseStyle.scaleHandler.brHandler(fabricObject.angle))}) 16 16, auto`,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        backgroundColor: '#fff',
        icon: '',
        apply: ['image', 'text', 'container', 'video', 'extendRect'],
      },
      {
        key: 'bl',
        cursor: (
          _eventData: TPointerEvent,
          _control: Control,
          fabricObject: InteractiveFabricObject,
      ) => `url(${svgToBase64(ownDefaultsMouseStyle.scaleHandler.blHandler(fabricObject.angle))}) 16 16, auto`,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        backgroundColor: '#fff',
        icon: '',
        apply: ['image', 'text', 'container', 'video', 'extendRect'],
      },
]