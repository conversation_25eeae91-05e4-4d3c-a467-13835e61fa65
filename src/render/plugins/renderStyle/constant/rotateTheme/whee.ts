import { z } from "zod/v4";
import { controlRotateSchema } from "../../schema/control";
import { InteractiveFabricObject } from "node_modules/fabric/dist/src/shapes/Object/InteractiveObject";
import { Control } from "fabric";
import { TPointerEvent } from "fabric";
import { svgToBase64 } from "../../../../utils";
import { ownDefaultsMouseStyle } from "../../../../base/ownDefaults";

const applyArray = ['image', 'text', 'container', 'video']

export const WheeRotateTheme: Array<z.infer<typeof controlRotateSchema>> = [
    {
        key: 'mtr',
        show: true,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        offsetX: 6,
        offsetY: -12,
        backgroundColor: '#FFFFFF',
        icon: '',
        apply: applyArray,  
        cursor: (
            _eventData: TPointerEvent,
            _control: Control,
            fabricObject: InteractiveFabricObject,
        ) => `url(${svgToBase64(ownDefaultsMouseStyle.rotateHandler.mtlHandler(fabricObject.angle))}) 16 16, auto`
    },
    {
        key: 'mtl',
        show: true,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        offsetX: -12,
        offsetY: -12,
        backgroundColor: '#FFFFFF',
        icon: '',
        apply: applyArray,
        cursor: (
            _eventData: TPointerEvent,
            _control: Control,
            fabricObject: InteractiveFabricObject,
        ) => `url(${svgToBase64(ownDefaultsMouseStyle.rotateHandler.mtlHandler(fabricObject.angle))}) 16 16, auto`
    },
    {
        key: 'mbl',
        show: true,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        offsetX: -6,
        offsetY: 12,
        backgroundColor: '#FFFFFF',
        icon: '',
        apply: applyArray,
        cursor: (
            _eventData: TPointerEvent,
            _control: Control,
            fabricObject: InteractiveFabricObject,
        ) => `url(${svgToBase64(ownDefaultsMouseStyle.rotateHandler.mtlHandler(fabricObject.angle))}) 16 16, auto`
    },
    {
        key: 'mbr',
        show: true,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        offsetX: 6,
        offsetY: 12,
        backgroundColor: '#FFFFFF',
        icon: '',
        apply: applyArray,
        cursor: (
            _eventData: TPointerEvent,
            _control: Control,
            fabricObject: InteractiveFabricObject,
        ) => `url(${svgToBase64(ownDefaultsMouseStyle.rotateHandler.mtlHandler(fabricObject.angle))}) 16 16, auto`
    },

]