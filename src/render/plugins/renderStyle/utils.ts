import { z } from 'zod/v4'
import { ActiveSelection, Control, controlsUtils } from "fabric";
import { ElementName } from "../../utils";
import { selectionColorsSchema } from "./schema/selection";
import { RenderStyle } from './renderStyle';


export const createScaleControl = () => {
    return new Control({
        visible: true,
        actionName: 'scale',
        angle: 0,
        actionHandler: controlsUtils.scalingEqually,
    })
}

export const schemaToSelectionColor = (shapeName: ElementName): (keyof z.infer<typeof selectionColorsSchema>) => {
    if (shapeName === ElementName.FRAME) {
        return "defaultFrameSelectionColor"
    }
    if (shapeName === ElementName.IMAGE) {
        return "defaultImageSelectionColor"
    }
    if (shapeName === ElementName.TEXT) {
        return "defaultTextSelectionColor"
    }
    if (shapeName === ElementName.CONTAINER) {
        return "defaultContainerSelectionColor"
    }
    return "defaultSelectionColor"
}

/**
 * 根据预设值决策图形是否展示控制器
 * @param this 
 */
export function setShaPeControlBySettings(this: RenderStyle) {
    const selectionObjects = this._render._FC.getActiveObjects()
    // 单选
    if (selectionObjects.length === 1) {
        const object = selectionObjects[0]
        const shapeName = object._name_
        this.options.scale.forEach((item) => {
            object.setControlVisible(item.key, item.apply.includes(shapeName))
        })
        this.options.rotate.forEach((item) => {
            object.setControlVisible(item.key, item.apply.includes(shapeName))
        })
    }
    // 多选
    if (selectionObjects.length > 1) {
        const as = this._render._FC.getActiveObject()
        if (!(as instanceof ActiveSelection)) return;
        const map: Record<string, boolean> = {}
        this.options.scale.forEach(item => {
            const hasHideControl = selectionObjects.some(object => !item.apply.includes(object._name_))
            if (hasHideControl) {
                map[item.key] = false
            } else {
                map[item.key] = true
            }
        })
        this.options.rotate.forEach(item => {
            const hasHideControl = selectionObjects.some(object => !item.apply.includes(object._name_))
            if (hasHideControl) {
                map[item.key] = false
            } else {
                map[item.key] = true
            }
        })
        Object.keys(map).forEach(key => {
            as.setControlVisible(key, map[key])
        })
    }
    if (this._render._FC._activeObject) {
        this._render._FC._activeObject.controls = this._scaleControls
        this._render._FC._activeObject.setCoords()
        this._render._FC.renderAll()
    }
}