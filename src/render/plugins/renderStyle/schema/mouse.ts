import { z } from "zod/v4";
import { ownDefaultsMouseStyle } from "../../../base/ownDefaults/ownDefaults";

export const mouseRotateHandlerSchema = z.function().input([z.number()]).output(z.string());

export const mouseScaleHandlerSchema = z.function().input([z.number()]).output(z.string());

export const rotateHandlerSchema = z.object({
    mtlHandler: mouseRotateHandlerSchema,
    mtrHandler: mouseRotateHandlerSchema,
    mblHandler: mouseRotateHandlerSchema,
    mbrHandler: mouseRotateHandlerSchema,
});

export const scaleHandlerSchema = z.object({
    blHandler: mouseScaleHandlerSchema,
    brHandler: mouseScaleHandlerSchema,
    tlHandler: mouseScaleHandlerSchema,
    trHandler: mouseScaleHandlerSchema,
});

export const mouseSchema = z.object({
    defaults: z.string().optional().default(ownDefaultsMouseStyle.defaults).describe('默认鼠标样式'),
    hover: z.string().optional().default(ownDefaultsMouseStyle.hover).describe('悬停鼠标样式'),
    move: z.string().optional().default(ownDefaultsMouseStyle.move).describe('移动鼠标样式'),
    mousedown: z.string().optional().default(ownDefaultsMouseStyle.mousedown).describe('按下鼠标样式'),
    // rotateHandler: rotateHandlerSchema.optional().default(rotateHandlerSchema.parse({})),
    // scaleHandler: scaleHandlerSchema.optional().default(scaleHandlerSchema.parse({})),
});