import { z } from 'zod/v4';

import { rotateRenderControlsSchema, scaleRenderControlsSchema } from './control';
import { basicSchema } from './basic';
import { selectionColorsSchema } from './selection';
import { mouseSchema } from './mouse';
import { frameSchema } from './frame';

export const renderThemeSchema = z.object({
    basic: basicSchema.optional().default(basicSchema.parse({})),
    scale: scaleRenderControlsSchema.optional().default(scaleRenderControlsSchema.parse([])),
    selection: selectionColorsSchema.optional().default(selectionColorsSchema.parse({})),
    mouse: mouseSchema.optional().default(mouseSchema.parse({})),
    frame: frameSchema.optional().default(frameSchema.parse({})),
    rotate: rotateRenderControlsSchema.optional().default(rotateRenderControlsSchema.parse([])),
});