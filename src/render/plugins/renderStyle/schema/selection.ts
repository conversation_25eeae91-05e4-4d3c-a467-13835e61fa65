import { z } from "zod/v4";



export const selectionColorsSchema = z.object({
    // 默认选中颜色
    defaultSelectionColor: z.string().optional().default('#43464D').describe('默认选中颜色'),
    // 多选选中颜色
    defaultMultiSelectionColor: z.string().optional().default('#43464D').describe('多选选中颜色'),
    // 画板选中颜色
    defaultFrameSelectionColor: z.string().optional().default('#53F6B4').describe('画板选中颜色'),
    // 图片选中颜色
    defaultImageSelectionColor: z.string().optional().default('#F761FF').describe('图片选中颜色'),
    // 文本选中颜色
    defaultTextSelectionColor: z.string().optional().default('#43464D').describe('文本选中颜色'),
    // 容器选中颜色
    defaultContainerSelectionColor: z.string().optional().default('#43464D').describe('容器选中颜色'),
    // 选中边框宽度
    borderWidth: z.number().optional().default(1).describe('选中边框宽度'),
});



