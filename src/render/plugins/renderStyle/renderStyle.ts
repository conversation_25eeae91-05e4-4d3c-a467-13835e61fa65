import { z } from "zod/v4";
import { Plugin } from "../../base/types";
import { Render } from "../../render";
import { CanvasMouseStyle, RotateHandlerCursorByString } from "./types";
import { ActiveSelection, CanvasEvents, Control, FabricObject, InteractiveFabricObject, TPointerEvent, TPointerEventInfo } from "fabric";
import { schemaToSelectionColor, setShaPeControlBySettings } from "./utils";
import { ElementName, svgToBase64 } from "../../utils";
import { renderThemeSchema } from "./schema";
import { controlMouseDownHandler, controlMouseUpHandler, createRotateControl, ownDefaultsMouseStyle } from "../../base/ownDefaults";
import { ControlRenderingStyleOverride } from "node_modules/fabric/dist/src/controls/controlRendering";
import { setFramePreset } from "../../base/package/preset";
import { merge } from "lodash-es";
import { controlPointSchema, controlRotateSchema } from "./schema/control";
import { rotatePosition } from "./constant/rotatePosition";

type OptionsParams = z.infer<typeof renderThemeSchema>

export class RenderStyle extends Plugin {
    public __name__ = 'RenderStyle';
    private _events: Record<
        string,
        Array<(e: TPointerEventInfo<TPointerEvent>) => void>
    > = {};
    private _passivelyEvents: Record<
        string,
        Array<(e: TPointerEventInfo<TPointerEvent>) => void>
    > = {};
    public options: OptionsParams

    public _scaleControls: Record<string, Control> = {}

    private setShapeControlPassivelyEvents = setShaPeControlBySettings.bind(this)

    constructor(private render: Render, options?: Partial<OptionsParams>) {
        super(render);
        if (!options) {
            options = {}
        }
        const result = renderThemeSchema.safeParse(options)
        if (!result.success) {
            throw new Error(result.error.message)
        }
        this.options = result.data as OptionsParams
        setFramePreset(this.options.frame)
        this._events = {
            'mouse:down': [this._handleMouseDown],
            'mouse:up': [this._handleMouseUp],
        }
        this._passivelyEvents = {
            'object:rotating': [this._handleObjectRotating],
            'selection:created': [this._onSelectionCreated, this.setShapeControlPassivelyEvents],
            'selection:updated': [this._onSelectionUpdated, this.setShapeControlPassivelyEvents],
        }
        this.bindEvents(this._passivelyEvents);
        this._init();
    }

    private bindEvents(events: typeof this._events | typeof this._passivelyEvents): void {
        Object.entries(events).forEach(([event, handlers]) =>
            handlers.forEach((handler) =>
                this._render._FC.on(event as keyof CanvasEvents, handler)
            )
        );
    }
    private unbindEvents(events: typeof this._events | typeof this._passivelyEvents): void {
        Object.entries(events).forEach(([event, handlers]) =>
            handlers.forEach((handler) =>
                this._render._FC.off(event as keyof CanvasEvents, handler)
            )
        );
    }
    public __destroy__ = () => {
        this.unbindEvents(this._events);
        this.unbindEvents(this._passivelyEvents);
    }

    private _init = () => {
        this._setCanvasCursorStyle();
        this._scaleControls = this._setScaleFactor();
        this._setSelectionLineWidth();
    }

    public setScale = (options: typeof this.options.scale) => {
        this.options.scale = merge(this.options.scale, options)
        const focusObjects = this.render._FC.getActiveObjects()
        this.render._FC.discardActiveObject()
        this._init()
        this.render._FC.renderAll()
        if (focusObjects.length === 0) return
        if (focusObjects.length === 1) {
            this.render._FC.setActiveObject(focusObjects[0])
            this.render._FC.fire('selection:created')
        } else {
            const activeObject = new ActiveSelection()
            activeObject.add(...focusObjects)
            this.render._FC.setActiveObject(activeObject)
            this.render._FC.fire('selection:created')
        }
    }

    /**
     * 设置画布的鼠标样式
     */
    private _setCanvasCursorStyle = () => {
        const { defaults, hover: hoverCursor, move: moveCursor } = this.options.mouse;
        this.render._FC.defaultCursor = defaults;
        this.render._FC.hoverCursor = hoverCursor;
        this.render._FC.moveCursor = moveCursor;
    }
    /**
     * 设置选中边框宽度
     */
    private _setSelectionLineWidth = () => {
        // 设置全局默认的边框宽度
        InteractiveFabricObject.ownDefaults = {
            ...InteractiveFabricObject.ownDefaults,
            borderScaleFactor: this.options.selection.borderWidth,
        };
    }

    private _setScaleFactor = () => {
        const controls = FabricObject.createControls().controls;
        const controlMap: Record<string, Control> = {};
        this.options.scale.forEach(item => {
            const c = controls[item.key]
            if (typeof item.cursor === 'function') {
                c.cursorStyleHandler = item.cursor
            } 
            if (typeof item.cursor === 'string') {
                c.cursorStyle = item.cursor
            }
            let icon: HTMLImageElement | undefined
            if (item.icon) {
                icon = new Image()
                icon.src = item.icon
            }
            c.render = this.customScaleControlRender(item, icon)
            c.mouseDownHandler = controlMouseDownHandler
            c.mouseUpHandler = controlMouseUpHandler
            controlMap[item.key] = c
        })
        this.options.rotate.forEach(item => {
            const c = createRotateControl(
                rotatePosition[item.key].x,
                rotatePosition[item.key].y,
                item.offsetX,
                item.offsetY,
                item.cursor,
            )
            let icon: HTMLImageElement | undefined
            if (item.icon) {
                icon = new Image()
                icon.src = item.icon
            }
            c.render = this.customRotateControlRender(item, icon)
            if (typeof item.cursor === 'function') {
                c.cursorStyleHandler = item.cursor
            } 
            if (typeof item.cursor === 'string') {
                c.cursorStyle = item.cursor
            }
            controlMap[item.key] = c
        })
        InteractiveFabricObject.ownDefaults = {
            ...InteractiveFabricObject.ownDefaults,
            controls: {
                ...controlMap,
            },
        }
        return controlMap
    }
    /**
     * 设置画布的鼠标样式
     * @param style 
     */
    public setCursorStyle = (style: Partial<CanvasMouseStyle & { mousedown: string }>) => {
        this.options.mouse = merge(this.options.mouse, style)
        this.render._FC.defaultCursor = this.options.mouse.defaults;
        this.render._FC.hoverCursor = this.options.mouse.hover;
        this.render._FC.moveCursor = this.options.mouse.move;


        const canvas = document.querySelector('.upper-canvas') as HTMLElement;
        if (canvas) {
            canvas.style.cursor = this.options.mouse.defaults;
        }
        if (style.mousedown) {
            this.bindEvents(this._events);
        } else {
            this.unbindEvents(this._events);
        }
    }

    private _handleMouseDown = (_e: TPointerEventInfo<TPointerEvent>) => {
        const canvas = document.querySelector('.upper-canvas') as HTMLElement;
        if (canvas) {
            canvas.style.cursor = this.options.mouse.mousedown;
        }
        this.render._FC.defaultCursor = this.options.mouse.mousedown;
        this.render._FC.hoverCursor = this.options.mouse.mousedown;
        this.render._FC.moveCursor = this.options.mouse.mousedown;
    }

    private _handleMouseUp = (_e: TPointerEventInfo<TPointerEvent>) => {
        const canvas = document.querySelector('.upper-canvas') as HTMLElement;
        if (canvas) {
            canvas.style.cursor = this.options.mouse.defaults;
        }
        this.render._FC.defaultCursor = this.options.mouse.defaults;
        this.render._FC.hoverCursor = this.options.mouse.hover;
        this.render._FC.moveCursor = this.options.mouse.move;
    }

    private _handleObjectRotating = (e: TPointerEventInfo<TPointerEvent>) => {
        const transform = e.transform
        const target = e.target
        if (!transform || !target) return
        const corner = transform.corner as string
        const cursorData = this.options.rotate.find(item => item.key === corner)?.cursor
        if (typeof cursorData === 'function') {
            const angle = target.angle
            const cursor = cursorData(angle)
            this._render._FC.upperCanvasEl.style.cursor = cursor
            return
        }
        if (typeof cursorData === 'string') {
            this._render._FC.upperCanvasEl.style.cursor = cursorData
            return
        }
        const handler = ownDefaultsMouseStyle.rotateHandler[`${corner}Handler` as keyof RotateHandlerCursorByString]
        const angle = target.angle
        const cursor = `url(${svgToBase64(handler(angle))}) 16 16, auto`
        this._render._FC.upperCanvasEl.style.cursor = cursor
    }



    /**
 * 自定义缩放控制渲染
 * @param ctx 
 * @param left 
 * @param top 
 */
    private customScaleControlRender = (control: z.infer<typeof controlPointSchema>, icon?: HTMLImageElement) => {
        return (
            ctx: CanvasRenderingContext2D,
            left: number,
            top: number,
            _styleOverride: ControlRenderingStyleOverride | undefined,
            fabricObject: InteractiveFabricObject,
        ) => {
            const angle = fabricObject.getTotalAngle();
            const controls = fabricObject.controls
            const shape = control.shape
            const xSize = control.xSize
            const ySize = control.ySize
            const xSizeBy2 = xSize / 2
            const ySizeBy2 = ySize / 2
            const isTransparent = Object.keys(controls).some(key => {
                return controls[key as keyof typeof controls].isTransparent;
            });

            ctx.save();

            ctx.globalAlpha = 1;
            if (isTransparent) {
                ctx.globalAlpha = 0.1;
            }
            ctx.translate(left, top);
            ctx.rotate(angle * (Math.PI / 180));
            ctx.fillStyle = control.backgroundColor;
            const borderColor = this.options.selection[schemaToSelectionColor(fabricObject.get('_name_') as ElementName)] as string
            ctx.strokeStyle = borderColor;
            ctx.lineWidth = this.options.selection.borderWidth;
            ctx.beginPath();

            if (shape === 'ellipse') {
                ctx.ellipse(0, 0, xSizeBy2, ySizeBy2, 0, 0, Math.PI * 2);
            } else {
                ctx.rect(-xSizeBy2, -ySizeBy2, xSize, ySize);
            }
            ctx.fill();
            ctx.stroke();
            if (icon && icon.complete) {
                ctx.drawImage(icon, -xSizeBy2, -ySizeBy2, xSize, ySize)
            }

            ctx.restore();
        }
    }

    private customRotateControlRender = (
        control: z.infer<typeof controlRotateSchema>,
        icon?: HTMLImageElement
      ) => {
        return (
            ctx: CanvasRenderingContext2D,
            left: number,
            top: number,
        ) => {
            ctx.save();
            if (!control.show) {
                ctx.globalAlpha = 0;
            } else {
                ctx.globalAlpha = 1;
            }
            ctx.beginPath();
            if (control.shape === 'ellipse') {
                ctx.ellipse(left, top, control.xSize / 2, control.ySize / 2, 0, 0, Math.PI * 2);
            } else {
                ctx.rect(left, top, control.xSize, control.ySize);
            }
            ctx.fillStyle = control.backgroundColor;
            ctx.lineWidth = 1;
            
            ctx.fill();
            if (icon) {
                ctx.drawImage(icon, left - control.xSize / 2, top - control.ySize / 2, control.xSize, control.ySize);
            }
            ctx.restore();
        }
      }
    

    /**
     * 更新选中元素的边框颜色和宽度
     */
    private _updateSelectionColor = () => {
        const target = this.render._FC.getActiveObject()
        const borderWidth = this.options.selection.borderWidth

        if (target instanceof ActiveSelection) {
            const objects = target.getObjects()
            objects.forEach(item => {
                const color = this.options.selection[schemaToSelectionColor(item._name_ as ElementName)] as string
                item.set('borderColor', color)
                item.set('borderScaleFactor', borderWidth)
            })
        }
        const color = this.options.selection[schemaToSelectionColor(target?._name_ as ElementName)] as string
        target?.set('borderColor', color)
        target?.set('borderScaleFactor', borderWidth)
        return
    }
    /**
     * 创建选中元素的边框颜色
     */
    private _onSelectionCreated = () => {
        this._updateSelectionColor();
    }
    /**
     * 更新选中元素的边框颜色
     */
    private _onSelectionUpdated = () => {
        this._updateSelectionColor();
    }

}
