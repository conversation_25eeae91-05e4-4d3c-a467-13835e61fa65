import { z } from "zod";

export const extendOptionsSchema = z.object({
    renderMask: z.boolean().optional().default(false), // 是否开启渲染遮罩
    renderMaskColor: z.string().optional().default('#000000'), // 渲染遮罩颜色
    renderMaskOpacity: z.number().optional().default(0.5), // 渲染遮罩透明度
    maxExtendWidth: z.number(), // 最大扩展宽度
    maxExtendHeight: z.number(), // 最大扩展高度
    minExtendWidth: z.number(), // 最小扩展宽度
    minExtendHeight: z.number(), // 最小扩展高度
    initExtendWidth: z.number(), // 初始扩展宽度
    initExtendHeight: z.number(), // 初始扩展高度
    initExtendLeft: z.number(), // 初始扩展左
    initExtendTop: z.number(), // 初始扩展上
    initExtendFill: z.string(), // 初始扩展填充颜色
    initExtendOpacity: z.number(), // 初始扩展透明度
    // allowImageOutOfExtend: z.boolean().optional().default(false), // 是否允许图片超出扩展范围
})