type ScaleMapping = Record<string, { originX: string, originY: string }>
export const scaleMapping: ScaleMapping = {
    'tr': {
        originX: 'left',
        originY: 'bottom',
    },
    'tl': {
        originX: 'right',
        originY: 'bottom',
    },
    'br': {
        originX: 'left',
        originY: 'top',
    },
    'bl': {
        originX: 'right',
        originY: 'top',
    },
    'mt': {
        originX: 'center',
        originY: 'bottom',
    },
    'mb': {
        originX: 'center',
        originY: 'top',
    },
    'ml': {
        originX: 'right',
        originY: 'center',
    },
    'mr': {
        originX: 'left',
        originY: 'center',
    }
}