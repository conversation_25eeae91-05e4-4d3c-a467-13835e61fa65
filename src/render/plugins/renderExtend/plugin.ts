import { z } from 'zod';
import { Plugin } from '../../base/types';
import { Render } from '../../render';
import { extendOptionsSchema } from './schema';
import { renderMask, viewportTranslateHandler, viewportZoomHandler } from '../utils/mask';
import { Group, Rect, Canvas, BasicTransformEvent, TPointerEvent } from 'fabric';
import { IImage } from '../../base/package/image/image';
import { createExtendRect } from '../utils/extendRect';
import { scaleMapping } from './constant';
import { cornerBoundsHandler, extendContainerCornerBoundsHandler } from './utils';

type OptionsParams = z.infer<typeof extendOptionsSchema>
export class RenderExtend extends Plugin {
    public __name__ = 'RenderExtend';
    public options: OptionsParams;
    private render: Render;
    private maskElement: Rect | undefined;

    private _viewportTranslateHandler;
    private _viewportZoomHandler;
    private _extendContainer: Group
    private _extendTarget: IImage

    private targetParent: Group | Canvas
    private targetParentIndex: number


    constructor(render: Render, options: OptionsParams & { target: IImage }) {
        super(render);
        this.render = render;
        const result = extendOptionsSchema.parse(options);
        this.options = result;
        this._extendTarget = options.target
        if (this.options.renderMask) {
            this.maskElement = renderMask.call(render, {
                maskColor: this.options.renderMaskColor,
                maskOpacity: this.options.renderMaskOpacity,
            });
            this._viewportTranslateHandler = viewportTranslateHandler.bind(this.maskElement, render);
            this._viewportZoomHandler = viewportZoomHandler.bind(this.maskElement, render);
            render._FC.on('viewport:translate', this._viewportTranslateHandler);
            render._FC.on('viewport:zoom', this._viewportZoomHandler);
        }
        this._extendContainer = createExtendRect.call(this.render, {
            width: this.options.initExtendWidth,
            height: this.options.initExtendHeight,
            extendLeft: this.options.initExtendLeft,
            extendTop: this.options.initExtendTop,
            extendFill: this.options.initExtendFill,
            extendOpacity: this.options.initExtendOpacity,
            maxExtendWidth: this.options.maxExtendWidth,
            maxExtendHeight: this.options.maxExtendHeight,
            minExtendWidth: this.options.minExtendWidth,
            minExtendHeight: this.options.minExtendHeight,
            // allowImageOutOfExtend: this.options.allowImageOutOfExtend,
        })
        this.targetParent = this._extendTarget.parent || this.render._FC
        this.targetParentIndex = this.targetParent._objects.indexOf(this._extendTarget)
        // 从原有父级中移除
        this.targetParent._objects.splice(this.targetParentIndex, 1)
        // 添加到画布中最上级
        this.render._FC._objects.push(this._extendTarget)
        this.render._FC.renderAll()
        this.bindEvent()
    }


    private _extendContainerMovingHandler = () => {
        const bounds = this._getExtendPosition()
        const left = this._extendContainer.getCenterPoint().x
        const top = this._extendContainer.getCenterPoint().y
        this._extendContainer.set({
            left: Math.max(bounds.minLeft, Math.min(left, bounds.maxLeft)),
            top: Math.max(bounds.minTop, Math.min(top, bounds.maxTop)),
        })
    }

    private _extendContainerScalingHandler = (e: BasicTransformEvent<TPointerEvent>) => {
        const { transform } = e
        const { originX: originXKey, originY: originYKey } = scaleMapping[transform.corner]
        const originX = this._extendContainer.originX
        const originY = this._extendContainer.originY
        if (originXKey !== originX || originYKey !== originY) {
            const { left, top } = extendContainerCornerBoundsHandler[transform.corner](this._extendTarget, this._extendContainer)
            this._extendContainer.set({
                originX: originXKey,
                originY: originYKey,
                left,
                top,
            })
        }
        const {
            maxScaleY,
            minScaleY,
            maxScaleX,
            minScaleX
        } = extendContainerCornerBoundsHandler[transform.corner](this._extendTarget, this._extendContainer)
        console.log(maxScaleY, minScaleY, this._extendContainer.scaleY)
        if (maxScaleX && minScaleX) {
            this._extendContainer.set({
                scaleX: Math.min(Math.max(this._extendContainer.scaleX, minScaleX), maxScaleX),
            })
        }
        if (maxScaleY && minScaleY) {
            this._extendContainer.set({
                scaleY: Math.min(Math.max(this._extendContainer.scaleY, minScaleY), maxScaleY),
            })
        }
    }

    private _extendTargetMovingHandler = () => {
        const left = this._extendTarget.getCenterPoint().x;
        const top = this._extendTarget.getCenterPoint().y;
        const bounds = this._getTargetPosition()
        this._extendTarget.set({
            left: Math.max(bounds.minLeft, Math.min(left, bounds.maxLeft)),
            top: Math.max(bounds.minTop, Math.min(top, bounds.maxTop)),
        })

    }

    private _extendTargetScalingHandler = (e: BasicTransformEvent<TPointerEvent>) => {
        const { transform } = e
        const { originX: originXKey, originY: originYKey } = scaleMapping[transform.corner]
        const originX = this._extendTarget.originX
        const originY = this._extendTarget.originY

        if (originXKey !== originX || originYKey !== originY) {
            const { left, top } = cornerBoundsHandler[transform.corner](this._extendTarget, this._extendContainer)
            this._extendTarget.set({
                originX: originXKey,
                originY: originYKey,
                left,
                top,
            })
        }
        const { maxScaleX, maxScaleY } = cornerBoundsHandler[transform.corner](this._extendTarget, this._extendContainer)
        const selfScale = Math.min(this._extendTarget.scaleX, this._extendTarget.scaleY)
        const realScale = Math.min(selfScale, Math.min(maxScaleX, maxScaleY))
        this._extendTarget.set({
            scaleX: realScale,
            scaleY: realScale,
        })
        this._extendTarget.setCoords()
    }

    private _extendTargetScaledEndHandler = () => {
        const defaultOrigin = 'center'
        const left = this._extendTarget.getCenterPoint().x
        const top = this._extendTarget.getCenterPoint().y
        if (
            this._extendTarget.originX !== defaultOrigin ||
            this._extendTarget.originY !== defaultOrigin
        ) {
            this._extendTarget.set({
                originX: defaultOrigin,
                originY: defaultOrigin,
                left,
                top,
            })
        }

    }

    private _extendContainerScaledEndHandler = () => {
        const defaultOrigin = 'center'
        const left = this._extendContainer.getCenterPoint().x
        const top = this._extendContainer.getCenterPoint().y
        if (
            this._extendContainer.originX !== defaultOrigin ||
            this._extendContainer.originY !== defaultOrigin
        ) {
            this._extendContainer.set({
                originX: defaultOrigin,
                originY: defaultOrigin,
                left,
                top,
            })
        }

    }

    private bindEvent = () => {
        this._extendContainer.on('moving', this._extendContainerMovingHandler)
        this._extendTarget.on('moving', this._extendTargetMovingHandler)
        this._extendContainer.on('scaling', this._extendContainerScalingHandler)
        this._extendTarget.on('scaling', this._extendTargetScalingHandler)
        this._extendContainer.on('modified', this._extendContainerScaledEndHandler)
        this._extendTarget.on('modified', this._extendTargetScaledEndHandler)
    }
    private unBindEvent = () => {
        this._extendContainer.off('moving', this._extendContainerMovingHandler)
        this._extendTarget.off('moving', this._extendTargetMovingHandler)
        this._extendContainer.off('scaling', this._extendContainerScalingHandler)
        this._extendTarget.off('scaling', this._extendTargetScalingHandler)
        this._extendContainer.off('modified', this._extendContainerScaledEndHandler)
        this._extendTarget.off('modified', this._extendTargetScaledEndHandler)
    }


    private _getExtendPosition = () => {
        const extendTargetBottom = this._extendTarget.getScaledHeight() / 2 + this._extendTarget.getCenterPoint().y
        const extendTargetRight = this._extendTarget.getScaledWidth() / 2 + this._extendTarget.getCenterPoint().x
        const extendTargetTop = this._extendTarget.getCenterPoint().y - this._extendTarget.getScaledHeight() / 2
        const extendTargetLeft = this._extendTarget.getCenterPoint().x - this._extendTarget.getScaledWidth() / 2
        const extendContainerBottom = this._extendContainer.getScaledHeight() / 2 + this._extendContainer.getCenterPoint().y
        const extendContainerRight = this._extendContainer.getScaledWidth() / 2 + this._extendContainer.getCenterPoint().x
        const extendContainerTop = this._extendContainer.getCenterPoint().y - this._extendContainer.getScaledHeight() / 2
        const extendContainerLeft = this._extendContainer.getCenterPoint().x - this._extendContainer.getScaledWidth() / 2
        const extendContainerCenter = this._extendContainer.getCenterPoint()
        return {
            minLeft: extendContainerCenter.x - (extendContainerRight - extendTargetRight), // container 最小left
            maxLeft: extendTargetLeft - extendContainerLeft + extendContainerCenter.x, // container 最大left
            minTop: extendContainerCenter.y - (extendContainerBottom - extendTargetBottom), // container 最小top
            maxTop: extendTargetTop - extendContainerTop + extendContainerCenter.y, // container 最大top
        }

    }

    private _getTargetPosition = () => {
        const targetWidth = this._extendTarget.getScaledWidth();
        const targetHeight = this._extendTarget.getScaledHeight();
        const containerWidth = this._extendContainer.getScaledWidth()
        const containerHeight = this._extendContainer.getScaledHeight();
        const containerCenter = this._extendContainer.getCenterPoint();
        const minLeft = containerCenter.x - (containerWidth / 2 - targetWidth / 2);
        const maxLeft = containerCenter.x + (containerWidth / 2 - targetWidth / 2);
        const minTop = containerCenter.y - (containerHeight / 2 - targetHeight / 2);
        const maxTop = containerCenter.y + (containerHeight / 2 - targetHeight / 2);
        return {
            minLeft,
            maxLeft,
            minTop,
            maxTop,
        }
    }

    /**
     * 设置扩展容器的尺寸和位置
     * @param options 
     * @param options.width 扩展容器的宽度
     * @param options.height 扩展容器的高度
     * @param options.left 扩展容器的left
     * @param options.top 扩展容器的top
     */
    public setExtendContainerSizeAndPosition = (options: {
        width: number
        height: number
        left: number
        top: number
    }) => {
        this._extendContainer.set({
            width: options.width,
            height: options.height,
            left: options.left,
            top: options.top,
            scaleX: 1,
            scaleY: 1,
        })
        this._extendContainer.setCoords()
        this.render._FC.renderAll()
    }

    public getExtendContainerAndTargetBounds = () => {
        return {
            extendContainerBounds: {
                centerX: this._extendContainer.getCenterPoint().x,
                centerY: this._extendContainer.getCenterPoint().y,
                originWidth: this._extendContainer.width,
                originHeight: this._extendContainer.height,
                scaleX: this._extendContainer.scaleX,
                scaleY: this._extendContainer.scaleY,
                width: this._extendContainer.getScaledWidth(),
                height: this._extendContainer.getScaledHeight(),
            },
            targetBounds: {
                centerX: this._extendTarget.getCenterPoint().x, 
                centerY: this._extendTarget.getCenterPoint().y,
                originWidth: this._extendTarget.width,
                originHeight: this._extendTarget.height,
                scaleX: this._extendTarget.scaleX,
                scaleY: this._extendTarget.scaleY,
                width: this._extendTarget.getScaledWidth(),
                height: this._extendTarget.getScaledHeight(),
            },
        }
    }




    private _resetTarget = () => {
        const currentTargetParent = this._extendTarget.parent || this.render._FC
        const currentTargetParentIndex = currentTargetParent._objects.indexOf(this._extendTarget)
        // 从目前的父级中移除
        currentTargetParent._objects.splice(currentTargetParentIndex, 1)
        // 添加到历史中的原有层级
        this.targetParent._objects.splice(this.targetParentIndex, 1, this._extendTarget)
        this.render._FC.renderAll()
    }
    private _removeExtendContainer = () => {
        const currentExtendContainerParent = this._extendContainer.parent || this.render._FC
        const currentExtendContainerParentIndex = currentExtendContainerParent._objects.indexOf(this._extendContainer)
        // 从目前的父级中移除
        currentExtendContainerParent._objects.splice(currentExtendContainerParentIndex, 1)
        this.render._FC.renderAll()
    }
    private _removeExtendMask = () => {
        if (this.maskElement) {
            const currentExtendMaskParent = this.maskElement.parent || this.render._FC
            const currentExtendMaskParentIndex = currentExtendMaskParent._objects.indexOf(this.maskElement)
            // 从目前的父级中移除
            currentExtendMaskParent._objects.splice(currentExtendMaskParentIndex, 1)
            this.render._FC.renderAll()
        }
    }

    public __destroy__ = () => {
        this.unBindEvent()
        this._resetTarget()
        this._removeExtendContainer()
        this._removeExtendMask()
        if (this.options.renderMask) {
            if (this._viewportTranslateHandler) {
                this.render._FC.off('viewport:translate', this._viewportTranslateHandler);
            }
            if (this._viewportZoomHandler) {
                this.render._FC.off('viewport:zoom', this._viewportZoomHandler);
            }
        }
    }
}