import hotkeys from 'hotkeys-js';
import { Render } from '../../render';
import { Plugin } from '../../base/types';

type HotkeysOptions = Parameters<typeof hotkeys>

type RegisteredHotKey = HotkeysOptions;

/**
 * @deprecated
 * @description 该插件已废弃，未来版本将废弃该插件，请使用 hotkeys 代替
 */
export class HotKey extends Plugin {
  public __name__ = 'HotKey';
  private _registeredHotKeys: RegisteredHotKey[];
  constructor(_render: Render, registeredHotKey?: RegisteredHotKey[]) {
    super(_render);
    this._registeredHotKeys = registeredHotKey || [];
    this.initHotKey();
  }
  private initHotKey = () => {
    hotkeys.filter = () => true;
    this._registeredHotKeys.forEach((hotKey) => {
      hotkeys(...hotKey);
    });
  };

  /**
   * 销毁插件
   */
  public __destroy__ = () => {
    this._registeredHotKeys.forEach((hotKey) => {
      hotkeys.unbind(hotKey[0], hotKey[2]);
    });
  };

  /**
   * 注册热键
   * @param key
   * @param handler
   */
  public registerHotKey = (options: HotkeysOptions) => {
    hotkeys(...options);
    this._registeredHotKeys.push(options);
  };

  /**
   * 注销热键
   * @param key
   * @param handler
   */
  public unregisterHotKey = (options: HotkeysOptions) => {
    hotkeys.unbind(options[0], options[2]);
    this._registeredHotKeys = this._registeredHotKeys.filter(
      (hotKey) => hotKey[0] !== options[0] && hotKey[2] !== options[2]
    );
  };
}
