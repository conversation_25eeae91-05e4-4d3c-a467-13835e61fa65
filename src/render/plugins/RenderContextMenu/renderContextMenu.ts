import { ActiveSelection, CanvasEvents, TPointerEventInfo } from 'fabric';
import { TPointerEvent } from 'fabric';
import { Plugin } from '../../base/types';
import { Render } from '../../render';
import { createContextMenuContainer } from './render';
import { ContextMenuComponentProps } from './types';

export class ContextMenu<T> extends Plugin {
  public __name__ = 'ContextMenu';
  public _destroyContainer: () => void = () => { };
  private events: Partial<
    Record<
      keyof CanvasEvents,
      Array<(e: TPointerEventInfo<TPointerEvent>) => void>
    >
  > = {};

  constructor(
    _render: Render,
    private Component: React.ComponentType<ContextMenuComponentProps & T>,
    private opt: T,
    private cssStyle?: React.CSSProperties
  ) {
    super(_render);
    this.events = {
      'mouse:down': [this._handleMouseDown],
      contextmenu: [this._handleContextMenu],
    };
    this.bindEvents();
  }

  private _handleContextMenuByDocument = (event: MouseEvent) => {
    const target = event.target;
    if (target !== this._render._FC.upperCanvasEl) {
      this._destroyContainer?.();
    }
  };

  private bindEvents = () => {
    window.addEventListener('contextmenu', this._handleContextMenuByDocument);
    Object.entries(this.events).forEach(([event, handlers]) => {
      handlers.forEach((handler) => {
        this._render._FC.on(event as keyof CanvasEvents, handler);
      });
    });
  };

  public __destroy__ = () => {
    Object.entries(this.events).forEach(([event, handlers]) => {
      handlers.forEach((handler) => {
        this._render._FC.off(event as keyof CanvasEvents, handler);
      });
    });
    this._destroyContainer?.();
    window.removeEventListener('contextmenu', this._handleContextMenuByDocument);
  };

  private _handleMouseDown = (_event: TPointerEventInfo<TPointerEvent>) => {
    this._destroyContainer?.();
  };

  private _handleContextMenu = (event: TPointerEventInfo<TPointerEvent>) => {
    if (this._render._FC.get('isDisabledContextMenu')) return;
    if (event.target) {
      this._render._FC.setActiveObject(event.target);
      this._render._FC.requestRenderAll();
    } else {
      this._render._FC.discardActiveObject();
      this._render._FC.requestRenderAll();
    }
    event.e.preventDefault();
    this._destroyContainer?.();
    const scenePoint = this._render._FC.getScenePoint(event.e)
    const position = {
      x: (event.e as MouseEvent).clientX,
      y: (event.e as MouseEvent).clientY,
    };
    const targets =
      event.target instanceof ActiveSelection
        ? event.target.getObjects()
        : event.target
          ? [event.target]
          : [];
    this._destroyContainer = createContextMenuContainer<T>(
      this.Component,
      {
        targets,
        canvas: this._render._FC,
        render: this._render,
        destroy: () => {
          this._destroyContainer?.();
        },
        position: {
          x: scenePoint.x,
          y: scenePoint.y,
        },
        ...this.opt,
      },
      position,
      this.cssStyle || {}
    );
  };
}
