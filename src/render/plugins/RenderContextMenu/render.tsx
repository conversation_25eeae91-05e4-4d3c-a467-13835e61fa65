import { createPortal } from 'react-dom';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { ContextMenuComponentProps } from './types';
const cssStyle = {
  position: 'fixed' as const,
  width: 'fit-content',
  height: 'fit-content',
  zIndex: 20000,
  left: 0,
  top: 0,
};

export const createContextMenuContainer = <T,>(
  Component: React.ComponentType<ContextMenuComponentProps & T>,
  props: ContextMenuComponentProps & T,
  position: {
    x: number;
    y: number;
  },
  _cssStyle: React.CSSProperties
) => {
  // 动态创建 div
  const div = document.createElement('div');
  const style = Object.assign({}, div.style, cssStyle, _cssStyle, {
    left: `${position.x}px`,
    top: `${position.y}px`,
  });
  Object.assign(div.style, style);
  document.body.appendChild(div);
  const portal = createPortal(<Component {...props} />, div);
  const root = createRoot(div);
  root.render(portal);
  // 使用 requestAnimationFrame 等待下一帧，确保组件已渲染
  requestAnimationFrame(() => {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const menuWidth = div.offsetWidth;
    const menuHeight = div.offsetHeight;

    // 计算最终位置，确保菜单在视口内
    const finalX = Math.min(position.x, windowWidth - menuWidth);
    const finalY = Math.min(position.y, windowHeight - menuHeight);

    // 确保不会出现负值
    const x = Math.max(0, finalX);
    const y = Math.max(0, finalY);

    // 应用最终位置
    Object.assign(div.style, {
      left: `${x}px`,
      top: `${y}px`,
    });
  });
  return () => {
    root.unmount();
    div.remove();
  };
};
