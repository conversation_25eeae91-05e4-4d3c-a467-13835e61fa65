import { Render } from '../../../render/render';
import { Plugin } from '../../base/types';
import { HistoryConfig } from './types';
import { History } from './history';
import { TextAction } from './actionEvent/textAction';
import { BaseAction } from './actionEvent/baseAction';
import { Operation } from './operation';
export class HistoryPlugins extends Plugin {
  public __name__ = 'History';
  public history: History;
  private textAction: TextAction;
  public baseAction: BaseAction;
  constructor(
    render: Render,
    options: {
      historyConfig?: HistoryConfig;
      isDisabledDefault?: boolean;
      isDisabledMask?: boolean;
      undoChangeCallback?: (operations: Operation[]) => void;
      redoChangeCallback?: (operations: Operation[]) => void;
      onInteractionModifiedCallback?: (operation: Operation) => void;
    } = {}
  ) {
    super(render, options);
    this.history = new History(
      options.historyConfig ?? {
        max: 9999,
      },
      render,
      options.undoChangeCallback,
      options.redoChangeCallback,
      options.onInteractionModifiedCallback
    );
    this.textAction = new TextAction(render, this.history);
    this.baseAction = new BaseAction(render, this.history, options.isDisabledDefault ?? false, options.isDisabledMask ?? false);
  }

  public setIsDisabledDefault = (isDisabledDefault: boolean) => {
    this.baseAction.setIsDisabledDefault(isDisabledDefault);
  };

  public __destroy__ = () => {
    this.textAction.destroy();
    this.baseAction.destroy();
  };

  public undo() {
    this.history.undo();
  }

  public redo() {
    this.history.redo();
  }

  public submit(operations: Operation | Operation[]) {
    this.history.submit(operations);
  }
}
