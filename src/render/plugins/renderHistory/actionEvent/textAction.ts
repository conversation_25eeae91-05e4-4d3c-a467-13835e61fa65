import { CanvasEvents, IText } from 'fabric';
import { Render } from '../../../../render/render';
import { Operation } from '../operation';
import { getElementOptions } from '../../../../render/utils/shapes';
import { DeltaType } from '../types';
import { History } from '../history';
export class TextAction {
  private _render: Render;
  private history: History;
  private textBeforeEdit: string = '';

  constructor(render: Render, history: History) {
    this._render = render;
    this.history = history;
    this.bindEvents();
  }

  private bindEvents(): void {
    this.handleEvent(true);
  }

  private handleEvent = (eventSwitch: boolean) => {
    const events = {
      'text:editing:entered': [this.handleTextEditingEntered],
      'text:editing:exited': [this.handleTextEditingExited],
    };

    Object.entries(events).forEach(([event, handlers]) =>
      handlers.forEach((handler) =>
        eventSwitch
          ? this._render._FC.on(event as keyof CanvasEvents, handler)
          : this._render._FC.off(event as keyof CanvasEvents, handler)
      )
    );
  };

  private handleTextEditingEntered = (e: { target: IText }) => {
    this.textBeforeEdit = e.target.text;
  };

  private handleTextEditingExited = (e: { target: IText }) => {
    if (!e.target) return;
    if (this.textBeforeEdit === e.target.text) return;
    const targetData = getElementOptions.call(this._render, e.target);
    const operation = new Operation(
      {
        type: DeltaType.MODIFY,
        selectable: true,
        preData: {
          objects: [
            {
              ...targetData,
              text: this.textBeforeEdit,
            },
          ],
        },
        afterData: {
          objects: [targetData],
        },
        undoHandler: undefined,
        redoHandler: undefined,
      },
      this._render
    );
    this.history.submit(operation);
    this.history.onInteractionModifiedCallback?.(operation);
  };

  public destroy = () => {
    this.handleEvent(false);
  };
}
