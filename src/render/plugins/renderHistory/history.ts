import { Operation } from './operation';
import { DeltaType, HistoryChangeType, HistoryConfig } from './types';
import { Render } from '../../../render/render';
import { ActiveSelection, Canvas, FabricObject } from 'fabric';
export class History {
  public redoStack: Operation[][] = [];
  public undoStack: Operation[][] = [];
  private _isContinue = false;
  constructor(
    private config: HistoryConfig,
    private render: Render,
    private undoChangeCallback?: (operations: Operation[]) => void,
    private redoChangeCallback?: (operations: Operation[]) => void,
    public onInteractionModifiedCallback?: (operation: Operation) => void
  ) {}

  public submit(operations: Operation | Operation[]) {
    if (this._isContinue) return;
    this.redoStack = [];
    if (!Array.isArray(operations)) {
      operations = [operations];
    }
    this.undoStack.push(operations);
    if (this.undoStack.length > this.config.max) {
      this.undoStack.shift();
    }
    this.render._FC.fire('history:changed', { type: HistoryChangeType.PUSH });
  }

  public async undo() {
    if (!this.undoStack.length) {
      return;
    }
    this._isContinue = true;
    let operations = this.undoStack.pop()!;
    await Promise.all(
      operations.map(async (operation) => {
        await operation.undo();
      })
    );
    this.handleSelect(operations, HistoryChangeType.UNDO);
    this.undoChangeCallback?.(operations);
    //UNDO剔除loading历史
    operations = operations.filter(
      (operation) => operation.options.type !== DeltaType.LOADING
    );
    operations.length && this.redoStack.push(operations);
    this._isContinue = false;
    this.render._FC.fire('history:changed', { type: HistoryChangeType.UNDO });
  }

  public async redo() {
    if (!this.redoStack.length) {
      return;
    }
    this._isContinue = true;
    const operations = this.redoStack.pop()!;
    await Promise.all(
      operations.map(async (operation) => {
        await operation.redo();
      })
    );
    this.handleSelect(operations, HistoryChangeType.REDO);
    this.undoStack.push(operations);
    this._isContinue = false;
    this.redoChangeCallback?.(operations);
    this.render._FC.fire('history:changed', { type: HistoryChangeType.REDO });
  }

  private handleSelect = (
    operations: Operation[],
    historyChangeType: HistoryChangeType
  ) => {
    if (this.config.isDisabledSelect) return;
    const instances: FabricObject[] = [];
    operations.forEach((operation) => {
      if (operation.options.selectable !== true) return;
      if (
        historyChangeType == HistoryChangeType.REDO &&
        operation.options.type == DeltaType.DELETE
      )
        return;
      if (
        historyChangeType == HistoryChangeType.UNDO &&
        operation.options.type == DeltaType.INSERT
      )
        return;
      const objects = (operation.options.preData ?? operation.options.afterData)
        ?.objects;
      objects?.forEach((object) => {
        const instance = this.render.Finder.findById(object._id_);
        if (instance instanceof Canvas) return;
        if (instance) instances.push(instance);
      });
    });
    if (instances.length > 1) {
      this.render._FC.setActiveObject(
        new ActiveSelection(instances, {
          canvas: this.render._FC,
        })
      );
    } else if (instances.length == 1) {
      this.render._FC.setActiveObject(instances[0]);
    } else {
      this.render._FC.discardActiveObject();
    }
  };

  public clear() {
    this.redoStack.length = 0;
    this.undoStack.length = 0;
    this.render._FC.fire('history:changed', { type: HistoryChangeType.CLEAR });
  }
}
