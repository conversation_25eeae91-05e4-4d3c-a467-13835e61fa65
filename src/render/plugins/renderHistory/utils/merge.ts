import { FabricObject } from "fabric";
import { Render } from "../../../render";
import { Operation } from "../operation";
import { createElement} from "../../../utils";

export async function rejectMerge(this: Render, operation: Operation) {
  const preData = operation.options.preData;
  const afterData = operation.options.afterData;
  if (!preData || !afterData) return;
  const { objects: preObjects } = preData;
  const { objects: afterObjects } = afterData;
  if (!preObjects || !afterObjects) return;
  const preShapes = await Promise.all(preObjects.map(createElement));
  const afterShapes = afterObjects.map(element => this.Finder.findById(element._id_) as FabricObject);
  this._FC.remove(...afterShapes)
  preShapes.forEach((shape) => {
    this._FC.insertAt(shape._z_index_, shape)
  })
}

export async function rejectSplit(this: Render, operation: Operation) {
    const preData = operation.options.preData;
    const afterData = operation.options.afterData;
    if (!preData || !afterData) return;
    const { objects: preObjects } = preData;
    const { objects: afterObjects } = afterData;
    if (!preObjects || !afterObjects) return;
    const preShapes = await Promise.all(preObjects.map(createElement));
    const afterShapes = afterObjects.map(element => this.Finder.findById(element._id_) as FabricObject);
    this._FC.remove(...afterShapes)
    preShapes.forEach((shape) => {
        this._FC.insertAt(shape._z_index_, shape)
    })
}

export async function resolveMerge(this: Render, operation: Operation) {
    const preData = operation.options.preData;
    const afterData = operation.options.afterData;
    if (!preData || !afterData) return;
    const { objects: preObjects } = preData;
    const { objects: afterObjects } = afterData;
    if (!preObjects || !afterObjects) return;
    const preShapes = preObjects.map(element => this.Finder.findById(element._id_) as FabricObject);
    const afterShapes = await Promise.all(afterObjects.map(createElement));
    this._FC.remove(...preShapes)
    afterShapes.forEach((shape) => {
        this._FC.insertAt(shape._z_index_, shape)
    })
}

export async function resolveSplit(this: Render, operation: Operation) {
    const preData = operation.options.preData;
    const afterData = operation.options.afterData;
    if (!preData || !afterData) return;
    const { objects: preObjects } = preData;
    const { objects: afterObjects } = afterData;
    if (!preObjects || !afterObjects) return;
    const preShapes = preObjects.map(element => this.Finder.findById(element._id_) as FabricObject)
    const afterShapes = await Promise.all(afterObjects.map(createElement));
    this._FC.remove(...preShapes)
    afterShapes.forEach((shape) => {
        this._FC.insertAt(shape._z_index_, shape)
    })
}
