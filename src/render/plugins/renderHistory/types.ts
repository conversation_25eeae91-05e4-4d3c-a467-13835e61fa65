import { ElementOptions } from '../../utils/types';
export interface HistoryConfig {
  max: number;
  isDisabledSelect?: boolean;
  isDisabledMask?: boolean;
}
export enum DeltaType {
  INSERT = 'insert',
  DELETE = 'delete',
  MODIFY = 'modify',
  FRONT = 'front',
  BACK = 'back',
  FORWARD = 'forward',
  BACKWARD = 'backward',
  CHANGE_Z_INDEX = 'changeZIndex',
  LOADING = 'loading',
  LOADED = 'loaded',
  Handler = 'handler',
  MERGE = 'merge',
  SPLIT = 'split',
}

type DataType<T extends DeltaType> = T extends DeltaType.INSERT
  ? {
      objects: ElementOptions[];
    }
  : T extends DeltaType.DELETE
  ? {
      objects: ElementOptions[];
    }
  : T extends DeltaType.MODIFY
  ? {
      objects: ElementOptions[];
    }
  : T extends DeltaType.FRONT
  ? {
      objects: ElementOptions[];
    }
  : T extends DeltaType.BACK
  ? {
      objects: ElementOptions[];
    }
  : T extends DeltaType.LOADING
  ? {
      objects: ElementOptions[];
    }
  : T extends DeltaType.LOADED
  ? {
      objects: ElementOptions[];
    }
  : T extends DeltaType.FORWARD
  ? {
      objects: ElementOptions[];
    }
  : T extends DeltaType.BACKWARD
  ? {
      objects: ElementOptions[];
    }
  : T extends DeltaType.MERGE
  ? {
      objects: ElementOptions[];
    }
  : T extends DeltaType.SPLIT
  ? {
      objects: ElementOptions[];
    }
  : never;

export interface Delta<T extends DeltaType, U = DataType<T>> {
  type: T;
  afterData: U | null;
  preData: U | null;
  undoHandler: T extends DeltaType.Handler ? () => void : undefined;
  redoHandler: T extends DeltaType.Handler ? () => void : undefined;
  selectable?: boolean;
}

export enum HistoryChangeType {
  UNDO = 'undo',
  REDO = 'redo',
  PUSH = 'push',
  CLEAR = 'clear',
}
