import { z } from "zod/v4";
import { BaseElementOptionsSchema } from "./basic";

export const MaskOptionsSchema = z.object({
    _parent_name_: z.string(),
    index: z.number(),
    showTitle: z.boolean(),
    showCloseBtn: z.boolean(),
    erase: z.boolean()
});


export const MaskRectOptionsSchema = BaseElementOptionsSchema.extend({
    width: z.number().positive(),
    height: z.number().positive(),
    fill: z.string(),
    angle: z.number()
}).extend(MaskOptionsSchema.shape)

export const MaskPolygonOptionsSchema = BaseElementOptionsSchema.extend({
    points: z.array(z.object({
        x: z.number(),
        y: z.number()
    })),
    fill: z.string()
}).extend(MaskOptionsSchema.shape)

export const MaskPathOptionsSchema = BaseElementOptionsSchema.extend({
    path: z.string(),
    stroke: z.string(),
    strokeWidth: z.number(),
    strokeLineCap: z.string(),
    strokeMiterLimit: z.number(),
    strokeLineJoin: z.string(),
    strokeDashArray: z.array(z.number())
}).extend(MaskOptionsSchema.shape)

export const MaskSmartOptionsSchema = BaseElementOptionsSchema.extend({
    img: z.any(),
    revertImage: z.any(),
    width: z.number().positive(),
    height: z.number().positive(),
    fill: z.string(),
    angle: z.number()
}).extend(MaskOptionsSchema.shape)


export const MaskOptionsGroupSchema = z.object({
    _id_: z.string(),
    _container_name_: z.string(),
    _opacity_: z.number(),
    _mask_options_: z.array(z.union([MaskRectOptionsSchema, MaskPolygonOptionsSchema, MaskPathOptionsSchema]))
})
