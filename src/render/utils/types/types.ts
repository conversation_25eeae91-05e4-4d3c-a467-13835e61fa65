import { <PERSON><PERSON><PERSON><PERSON>oader } from "../../base/package/animate/loaderByCenter";
import { Loader } from "../../base/package/animate/loader";
import { IImage } from "../../base/package/image/image";
import { LoadingType } from "../enum";
import { Point } from "fabric";
import { z } from "zod/v4";
import { IVideoOptionsSchema } from ".";

type ElementSize = {
  width: number;
  height: number;
};
/**
 * 基础元素参数
 */
type BaseElementOptions = {
  _parent_id_: string;
  _id_: string;
  _name_: string;
  _shape_name_: string;
  _loading_: boolean;
  _loading_element_: boolean;
  _message_id_: string;
  _old_prompt_: string;
  _new_prompt_: string;
  left: number;
  top: number;
  relativelyLeft: number;
  relativelyTop: number;
  scaleX: number;
  scaleY: number;
  flipX: boolean;
  flipY: boolean;
  angle: number;
  opacity: number;
  visible: boolean;
  globalCompositeOperation: GlobalCompositeOperation;
  _custom_data_history_: Record<string, any>;
  _custom_data_: Record<string, any>;
  zIndex?: number;
};

// mask 元素==================

type MaskOptions = {
  _parent_name_: string;
  index: number;
  showTitle: boolean;
  showCloseBtn: boolean;
  erase: boolean;
};
export type MaskRectOptions = BaseElementOptions &
  MaskOptions &
  MaskOptions & {
    width: number;
    height: number;
    fill: string;
    angle: number;
  };

export type MaskPolygonOptions = BaseElementOptions &
  MaskOptions & {
    points: Point[];
    fill: string;
  };

export type MaskPathOptions = BaseElementOptions &
  MaskOptions & {
    path: string;
    stroke: string;
    strokeWidth: number;
    strokeLineCap: CanvasLineCap;
    strokeMiterLimit: number;
    strokeLineJoin: CanvasLineJoin;
    strokeDashArray: number[];
  };

export type MaskSmartOptions = BaseElementOptions &
  MaskOptions & {
    img: HTMLImageElement;
    revertImage: HTMLImageElement;
    width: number;
    height: number;
    fill: string;
    angle: number;
  };

export type MaskOptionsGroup = {
  _id_: string;
  _container_name_: string;
  _opacity_: number;
  _mask_options_: (MaskRectOptions | MaskPolygonOptions | MaskPathOptions)[];
};

// ============================================================== 图片元素
export type ImageOptions = BaseElementOptions & {
  src: string;
  imageWidth: number;
  imageHeight: number;
};
export type ImageCustomOptions = BaseElementOptions &
  ElementSize & {
    _mask_options_: MaskOptionsGroup[];
  };
// ===============================================================

// Frame元素参数
export type FrameElementOptions = BaseElementOptions &
  ElementSize & {
    backgroundColor?: string;
    subTargetCheck?: boolean;
    interactive?: boolean;
    labelText?: string;
    labelFill?: string;
    labelTextFill?: string;
    labelFontSize?: number;
    children?: (
      | TextElementOptions
      | (ImageCustomOptions & ImageOptions)
      | FrameElementOptions
    )[];
  };

// 文本元素参数
export type TextElementOptions = BaseElementOptions &
  Partial<ElementSize> & {
    fontFamily: string;
    fontSize: number;
    fontStyle: string;
    textAlign: string;
    fontWeight: string | number;
    text: string;
    _font_url_: string;
    fill: string;
    underline: boolean;
    lineHeight: number;
    letterSpacing: number;
    linethrough: boolean; // 删除线
    stroke: string;
    strokeWidth: number;
    strokeLineCap: CanvasLineCap;
    strokeLineJoin: CanvasLineJoin;
    strokeDashArray: number[] | null;
  };

export type ContainerElementOptions = Omit<
  FrameElementOptions,
  "backgroundColor"
> & {
  children: (TextElementOptions | (ImageCustomOptions & ImageOptions))[];
  flipX: boolean;
  flipY: boolean;
};

export type ElementOptions =
  | (ImageOptions & ImageCustomOptions)
  | FrameElementOptions
  | TextElementOptions
  | ContainerElementOptions
  | z.infer<typeof IVideoOptionsSchema>

// 可选文本参数
export type TextElementParams = Partial<BaseElementOptions> &
  Partial<ElementSize> & {
    fontFamily: string;
    fontSize: number;
    fontWeight: string | number;
    text: string;
    fontUrl: string;
    fill?: string;
  };
// 可选图片参数
export type ImageElementParams = Partial<BaseElementOptions> &
  ElementSize & {
    src: string;
  };
// 可选容器参数
export type ContainerElementParams = Partial<BaseElementOptions> & {
  children?: (TextElementParams | ImageElementParams | ContainerElementParams)[];
};
// 可选帧参数
export type FrameElementParams = Partial<BaseElementOptions> & {
  width: number;
  height: number;
  backgroundColor: string;
  labelText?: string;
  labelFill?: string;
  labelTextFill?: string;
  labelFontSize?: number;
  children?: (
    | TextElementParams
    | ImageElementParams
    | ContainerElementParams
    | z.infer<typeof IVideoOptionsSchema>
  )[];
};

export type Area = {
  x: number;
  y: number;
  width: number;
  height: number;
};

/**
 * 设置loading后临时记录的参数
 */
export type LoadingOptions = {
  el: IImage;
  text: string;
  type: LoadingType;
  loadingTarget: Loader | CircleDanceLoader;
  blur?: boolean;
  maskOpacity?: number;
};

export type GetElementOptionsOptions = {
  maskOptions?: boolean;
};

