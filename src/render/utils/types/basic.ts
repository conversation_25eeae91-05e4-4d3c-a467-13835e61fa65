import { z } from "zod/v4";

const GlobalCompositeOperationEnum = z.enum([
    "source-over",
    "source-in",
    "source-out",
    "source-atop",
    "destination-over",
    "destination-in",
    "destination-out",
    "destination-atop",
    "lighter",
    "copy",
    "xor",
    "multiply",
    "screen",
    "overlay",
    "darken",
    "lighten",
    "color-dodge",
    "color-burn",
    "hard-light",
    "soft-light",
    "difference",
    "exclusion",
    "hue",
    "saturation",
    "color",
    "luminosity",
  ]).default("source-over");

export const ElementSizeSchema = z.object({
    width: z.number().positive(),
    height: z.number().positive(),
})


export const BaseElementOptionsSchema = z.object({
    _parent_id_: z.string(),
    _id_: z.string(),
    _name_: z.string(),
    _shape_name_: z.string(),
    _loading_: z.boolean(),
    _loading_element_: z.boolean(),
    _message_id_: z.string(),
    _old_prompt_: z.string(),
    _new_prompt_: z.string(),
    left: z.number(),
    top: z.number(),
    relativelyLeft: z.number(),
    relativelyTop: z.number(),
    scaleX: z.number(),
    scaleY: z.number(),
    flipX: z.boolean(),
    flipY: z.boolean(),
    angle: z.number(),
    opacity: z.number(),
    visible: z.boolean(),
    globalCompositeOperation: GlobalCompositeOperationEnum,
    _custom_data_history_: z.record(z.string(), z.any()),
    _custom_data_: z.record(z.string(), z.any()),
    zIndex: z.number().optional(),
})