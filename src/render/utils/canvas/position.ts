import { getViewPortVisibleArea, isObjectInArea, getVisibleObjects, isIntersectRect, getObjectBounds } from ".";
import { isRealElement, Render } from "../..";

/**
 * 根据视口中心和对象的排列方式获取对象的位置
 *
 * 此函数旨在计算一个对象（如图形或UI元素）在视口中的理想位置
 * 它首先尝试将对象中心对齐到视口中心，但如果对象与视口中已存在的其他对象重叠，
 * 它会调整对象的位置，以确保它不会与其他对象重叠这是通过增加对象的水平位置直到没有重叠为止来实现的
 *
 * @param this - Render实例，代表画布上下文
 * @param width - 对象的宽度，用于计算对象的尺寸和位置
 * @param height - 对象的高度，用于计算对象的尺寸和位置
 * @returns 返回一个包含对象理想位置的left和top坐标的对象
 */
export function getBoundToViewPortCenterByArrangementToPosition(
    this: Render,
    width: number,
    height: number
) {
    const area = {
        x: 0,
        y: 0,
        width,
        height,
    };
    const selected = this._FC.getActiveObject();
    const { centerX, centerY } = getViewPortVisibleArea(this._FC);
    area.x = centerX - width / 2;
    area.y = centerY - height / 2;

    if (selected) {
        area.x = selected.left - width / 2;
        area.y = selected.top - height / 2;
    }

    while (isObjectInArea.call(this._FC, area, false)) {
        area.x += area.width;
    }
    return {
        left: area.x + area.width / 2,
        top: area.y + area.height / 2,
    }
}


/**
 * 根据视口中心和对象的排列方式获取对象的位置
 *
 * 此函数旨在计算一个对象（如图形或UI元素）在视口中的理想位置
 * 它首先尝试将对象中心对齐到左下角，但如果对象与视口中已存在的其他对象重叠，
 * 它会调整对象的位置，以确保它不会与其他对象重叠这是通过增加对象的垂直位置直到没有重叠为止来实现的
 *
 * @param this - Render实例，代表画布上下文
 * @param width - 对象的宽度，用于计算对象的尺寸和位置
 * @param height - 对象的高度，用于计算对象的尺寸和位置
 * @returns 返回一个包含对象理想位置的left和top坐标的对象
 */
/**
 * 根据视口中心和对象的排列方式获取对象的位置
 *
 * 此函数旨在计算一个对象（如图形或UI元素）在视口中的理想位置
 * 它首先尝试将对象中心对齐到左下角，但如果对象与视口中已存在的其他对象重叠，
 * 它会调整对象的位置，以确保它不会与其他对象重叠这是通过增加对象的垂直位置直到没有重叠为止来实现的
 *
 * @param this - Render实例，代表画布上下文
 * @param width - 对象的宽度，用于计算对象的尺寸和位置
 * @param height - 对象的高度，用于计算对象的尺寸和位置
 * @returns 返回一个包含对象理想位置的left和top坐标的对象
 */
export function getBoundToLeftBottomByArrangementToPosition(
    this: Render,
    width: number,
    height: number
) {
    const area = {
        x: 0,
        y: 0,
        width,
        height,
    };
    const { left, top } = getViewPortVisibleArea(this._FC);
    area.x = left;
    area.y = top;

    while (isObjectInArea.call(this._FC, area, false)) {
        area.y += area.height;
    }
    return {
        left: area.x + area.width / 2,
        top: area.y + area.height / 2,
    }
}

/**
 * 根据视口中心和对象的排列方式获取对象的位置
 *
 * 此函数旨在计算一个对象（如图形或UI元素）在视口中的理想位置
 * 它首先尝试将对象中心对齐到视口中心，但如果对象与视口中已存在的其他对象重叠，
 * 它会调整对象的位置，以确保它不会与其他对象重叠这是通过增加对象的水平位置直到没有重叠为止来实现的
 *
 * @param this - Render实例，代表画布上下文
 * @param width - 对象的宽度，用于计算对象的尺寸和位置
 * @param height - 对象的高度，用于计算对象的尺寸和位置
 * @returns 返回一个包含对象理想位置的left和top坐标的对象
*/
export function collectViewportBoundsFromBottomLeft(
    this: Render,
    size: {
        width: number,
        height: number,
    },
    offsetX: number = 0,
    offsetY: number = 0,
    // 溢出可视区域是否继续匹配
    isContinueMatch: boolean = false
) {
    const { left, top, right, bottom } = getViewPortVisibleArea(this._FC);
    const inViewPortElements = getVisibleObjects(this._FC)
    if (inViewPortElements.length === 0) {
        return {
            left: left + size.width / 2,
            top: top + size.height / 2,
        }
    }
    const inViewPortElementsBounds = inViewPortElements.map((element) => {
        return {
            left: Math.min(...element.getCoords().map((coord) => coord.x)),
            top: Math.min(...element.getCoords().map((coord) => coord.y)),
            right: Math.max(...element.getCoords().map((coord) => coord.x)),
            bottom: Math.max(...element.getCoords().map((coord) => coord.y)),
        }
    })
    const position = {
        left: Math.min(...inViewPortElementsBounds.map((bound) => bound.left)),
        bottom: Math.max(...inViewPortElementsBounds.map((bound) => bound.bottom)),
    }
    const area = {
        left: position.left + offsetX,
        top: position.bottom + offsetY,
        right: position.left + size.width + offsetX,
        bottom: position.bottom + size.height + offsetY,
    }
    const allObjects = this._FC.getObjects().filter(isRealElement);

    function isObjectIntersectArea(): { status: boolean, objBounds: { left: number, top: number, right: number, bottom: number } } {
        if (allObjects.length === 0) {
            return {
                status: true,
                objBounds: area
            }
        }
        for (const obj of allObjects) {
            const objBounds = getObjectBounds(obj);
            if (isIntersectRect(objBounds, area)) {
                return {
                    status: false,
                    objBounds
                }
            }
        }
        return {
            status: true,
            objBounds: area
        }
    }
    let ok = false;
    while (!ok) {
        const { status, objBounds } = isObjectIntersectArea();
        ok = status;
        if (status) {
            return {
                left: area.left + size.width / 2 + offsetX,
                top: area.top + size.height / 2 + offsetY,
            }
        }
        if (isContinueMatch && !isIntersectRect(area, {
            left,
            top,
            right,
            bottom,
        })) {
            return {
                left: area.left + size.width / 2 + offsetX,
                top: area.top + size.height / 2 + offsetY,
            }
        }
        area.top = objBounds.bottom;
        area.bottom = area.top + size.height;
    }
    return {
        left: area.left + size.width / 2 + offsetX,
        top: area.top + size.height / 2 + offsetY,
    }
}

/**
 * 根据视口右边界和对象的排列方式获取对象的位置
 *
 * 此函数旨在计算一个对象（如图形或UI元素）在视口中的理想位置
 * 它首先尝试将对象中心对齐到视口右边界，但如果对象与视口中已存在的其他对象重叠，
 * 它会调整对象的位置，以确保它不会与其他对象重叠这是通过增加对象的水平位置直到没有重叠为止来实现的
 *
 * @param this - Render实例，代表画布上下文
 * @param width - 对象的宽度，用于计算对象的尺寸和位置
 * @param height - 对象的高度，用于计算对象的尺寸和位置
 * @returns 返回一个包含对象理想位置的left和top坐标的对象
 */
export function collectViewportBoundsFromRight(
    this: Render,
    size: {
        width: number,
        height: number,
        left: number,
        top: number,
    },
    offsetX: number = 0,
    offsetY: number = 0,
    isContinueMatch: boolean = false
) {
    const inViewPortElements = getVisibleObjects(this._FC)
    if (inViewPortElements.length === 0 && !isContinueMatch) {
        return {
            left: size.left + size.width / 2 + offsetX,
            top: size.top + size.height / 2 + offsetY,
        }
    }

    const allObjects = this._FC.getObjects().filter(isRealElement);
    function isObjectIntersectArea(): { status: boolean, objBounds: { left: number, top: number, right: number, bottom: number } } {
        if (allObjects.length === 0) {
            return {
                status: true,
                objBounds: {
                    left: size.left,
                    top: size.top,
                    right: size.left + size.width,
                    bottom: size.top + size.height,
                }
            }
        }
        for (const obj of allObjects) {
            const objBounds = getObjectBounds(obj);
            if (isIntersectRect(objBounds, {
                left: size.left + offsetX,
                top: size.top + offsetY,
                right: size.left + size.width + offsetX,
                bottom: size.top + size.height + offsetY,
            })) {
                return {
                    status: false,
                    objBounds
                };
            }
        }
        return {
            status: true,
            objBounds: {
                left: size.left,
                top: size.top,
                right: size.left + size.width,
                bottom: size.top + size.height,
            }
        }
    }
    let ok = false;
    while (!ok) {
        const { status, objBounds } = isObjectIntersectArea();
        ok = status;
        if (status) {
            return {
                left: size.left + size.width / 2 + offsetX,
                top: size.top + size.height / 2 + offsetY,
            }
        } else {
            // 当发生碰撞时,同时增加X和Y轴的偏移
            size.left = objBounds.right;
            size.top = size.top + offsetY;
        }
    }
    return {
        left: size.left + size.width / 2 + offsetX,
        top: size.top + size.height / 2 + offsetY,
    }
}
