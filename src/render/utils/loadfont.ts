import Logger from "../logger";
import { ElementName } from "./enum";
import { ContainerElementParams, ImageElementParams, TextElementParams, FrameElementParams, ElementOptions, TextElementOptions } from "./types";

interface ILoadFont {
    fontFamily: string;
    fontUrl: string;
}
/**
 * 加载字体
 * @param {ILoadFont[]} fonts 字体配置
 */
export const loadFont = async (fonts: ILoadFont[]) => {
    await document.fonts.ready;
    const readyFonts = [...document.fonts].map((font) => font.family);
    fonts = fonts.filter((font) => font.fontFamily && font.fontUrl);
    fonts = fonts.filter((font) => {
        let fontName = font.fontFamily.includes(' ') ? `"${font.fontFamily}"` : font.fontFamily;
        fontName = fontName.replace('.', '');
        return !readyFonts.includes(fontName);
    });
    if (fonts.length === 0) return;
    const fontFaces = fonts.map((font) => new Promise<FontFace>((resolve, reject) => {
        fetch(font.fontUrl, {
            mode: 'cors',
        })
        .then(response => {
            if (!response.ok) {
                Logger.error('load font error', response);
                return
            }
            return response.blob();
        })
        .then(blob => {
            if (!blob) {
                Logger.error('load font error', blob);
                return
            }
            const blobUrl = URL.createObjectURL(blob);
            const fontFace = new FontFace(font.fontFamily, `url(${blobUrl})`);
            fontFace.load().then(() => {
                document.fonts.add(fontFace);
                resolve(fontFace);
                URL.revokeObjectURL(blobUrl);
            }).catch(err => {
                Logger.error('load font error', err);
                reject(err);
            });
        })
        .catch(err => {
            Logger.error('load font error', err);
            reject(err);
        })
    }));
    return Promise.all(fontFaces).then(() => {
        Logger.info('load font success');
    }).catch(err => {
        Logger.error('load font error', err);
    });
};

export const findFontByData = (data: (TextElementParams | ImageElementParams | ContainerElementParams | FrameElementParams)[] | ElementOptions[]): ILoadFont[] => {
    if (!data || data.length === 0) {
        return [];
    }
    const fonts = data.filter((item) => item._name_ === ElementName.TEXT).map((item) => ({
        fontFamily: (item as TextElementParams).fontFamily,
        fontUrl: (item as TextElementParams).fontUrl || (item as TextElementOptions)._font_url_,
    }));
    const containerChildren = data.filter((item) => item._name_ === ElementName.CONTAINER).map((item) => (item as ContainerElementParams).children ?? []).flat();
    const frameChildren = data.filter((item) => item._name_ === ElementName.FRAME).map((item) => (item as FrameElementParams).children ?? []).flat();
    return [...fonts, ...findFontByData(containerChildren), ...findFontByData(frameChildren)];
}