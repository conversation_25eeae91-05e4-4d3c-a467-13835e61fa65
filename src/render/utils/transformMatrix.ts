import { Canvas, FabricObject, Group } from "fabric";

/**
 * 获取元素的变换矩阵
 * @param element 元素
 * @returns 变换矩阵
 */
export const transformMatrix = (element: FabricObject) => {
  const { scaleX, scaleY, flipX, flipY } = element.toObject()
  return {
    scaleX,
    scaleY,
    flipX,
    flipY,
  };
}
/**
 * 获取元素的相对位置
 * @param parent 父元素
 * @param element 元素
 * @returns 相对位置
 */
export const getRelativelyPosition = 
(parent: Group | Canvas, element: FabricObject | Group): {
  relativelyLeft: number;
  relativelyTop: number;
} => {
  const { left, top } = element.toObject()
  const isCanvas = parent instanceof Canvas
  if (isCanvas) {
    return {
      relativelyLeft: element.getCenterPoint().x,
      relativelyTop: element.getCenterPoint().y,
    }
  } else {
    return {
      relativelyLeft: left,
      relativelyTop: top,
    }
  }
}