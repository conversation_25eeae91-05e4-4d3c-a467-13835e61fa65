import to from 'await-to-js'
import Logger from "../logger";

export const getContainer = (container: string) => {
    const element = document.getElementById(container);
    if (!element) {
        throw new Error(`Container with id ${container} not found`);
    }
    const rect = element.getBoundingClientRect();
    return {
        element,
        width: rect.width,
        height: rect.height,
    };
}

export const createImageElement = async (src: string): Promise<HTMLImageElement> => new Promise((resolve, reject) => {
    const img = new Image();
    img.src = src;
    img.crossOrigin = 'Anonymous';
    img.onload = () => {
        resolve(img);
    }
    img.onerror = () => {
        reject(new Error(`Image with src ${src} not found`));
    }
});

export const createImageDataByOriginImage = async (src: string) => {
    const [err, image] = await to(createImageElement(src));
    if (err || !image) {
        Logger.error(`createImageDataByOriginImage error:`, err);
        return
    }
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) {
        Logger.error(`createImageDataByOriginImage error:`, "ctx is null");
        return
    }
    canvas.width = image.naturalWidth;
    canvas.height = image.naturalHeight;
    ctx.drawImage(image, 0, 0);
    return ctx.getImageData(0, 0, canvas.width, canvas.height);
}