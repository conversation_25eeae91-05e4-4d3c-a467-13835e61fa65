import { FabricObject, Point } from "fabric"
import { ElementName } from "../"
import { Render } from "../.."

/**
 * 判断元素是否为真实元素
 * @param element 元素
 * @returns 是否为真实元素
 */
export const isRealElement = (element: FabricObject) => {
    const elName = element._name_
    return elName === ElementName.IMAGE ||
        elName === ElementName.TEXT ||
        elName === ElementName.CONTAINER ||
        elName === ElementName.FRAME ||
        elName === ElementName.VIDEO
}

/**
 * 获取元素在屏幕中的位置
 * @param this 渲染器
 * @param element 元素
 * @returns 元素在屏幕中的位置
 */
export function getShapePositionByScreenBound(this: Render, ...els: FabricObject[]) {
    const canvas = this._FC

    const fn = (el: FabricObject) => {
        // 获取元素的所有角点坐标
        const coords = el.getCoords()
        if (!coords) return null

        const viewportTransform = canvas.viewportTransform
        if (!viewportTransform) return null

        // 将所有角点坐标转换为屏幕坐标
        const screenCoords = coords.map(point => point.transform(viewportTransform))
        
        // 计算包围盒
        const left = Math.min(...screenCoords.map(p => p.x))
        const top = Math.min(...screenCoords.map(p => p.y))
        const right = Math.max(...screenCoords.map(p => p.x))
        const bottom = Math.max(...screenCoords.map(p => p.y))

        return {
            left,
            top,
            width: right - left,
            height: bottom - top,
            right,
            bottom,
        }
    }

    const data = els.map(fn).filter(Boolean) as {
        left: number
        top: number
        right: number
        bottom: number
    }[]

    if (data.length === 0) return null

    // 计算所有元素的整体包围盒
    const left = Math.min(...data.map(item => item.left))
    const top = Math.min(...data.map(item => item.top))
    const right = Math.max(...data.map(item => item.right))
    const bottom = Math.max(...data.map(item => item.bottom))

    return {
        left,
        top,
        width: right - left,
        height: bottom - top,
        right,
        bottom,
    }
}