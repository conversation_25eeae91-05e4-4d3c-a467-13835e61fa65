import { nanoid } from 'nanoid';
import { ElementName } from '../enum';
import { getRelativelyPosition, transformMatrix } from '../transformMatrix';
import {
  ImageOptions,
  ImageCustomOptions,
  FrameElementOptions,
  TextElementOptions,
  ElementOptions,
  ContainerElementOptions,
  GetElementOptionsOptions,
  IVideoOptionsSchema,
} from '../types';
import {
  ContainerProperties,
  ErrorProperties,
  ImageProperties,
  VideoProperties,
} from '../../base/ownDefaults';
import {
  Group,
  LayoutManager,
  GroupProps,
  FabricImage,
  FixedLayout,
  FabricObject,
  Canvas,
  ImageProps,
  TextProps,
  IText,
} from 'fabric';
import { Frame } from '../../base/package/frame/frame';
import { Error as ErrorElement } from '../../base/package/animate/error';
import { IImage } from '../../base/package/image/image';
import Logger from '../../logger';
import { Render } from '../../render';
import { Container } from '../../base/package/container/container';
import { DefaultFontPreset } from '../../base/package/preset';
import { getMaskOptions } from './mask';
import { textEditingEntered, textEditingExited, textMouseDoubleClick } from './textEvent';
import { z } from 'zod/v4';
import { IVideo } from '../../base/package/video/video';
import { MaskOptionsGroupSchema } from '../types/mask';

export * from './Image';
export * from './element';
export * from './idHandler';

/**
 * 创建Frame元素
 * @param parent 父级元素
 * @param params 画布参数
 * @returns Frame元素
 */
export const createFrame = (
  parentId: string,
  params: Partial<GroupProps> & Record<string, any>,
  children?: FabricObject[]
) => {
  children = children?.sort((a, b) => b._z_index_ - a._z_index_) ?? []
  const frame = new Frame(children, {
    ...params,
    _name_: ElementName.FRAME,
    labelText: params.labelText || 'Frame',
    _id_: params._id_ || nanoid(),
    interactive: true,
    subTargetCheck: true,
    _parent_id_: parentId,
    _loading_: false,
    fill: params.backgroundColor,
    _z_index_: params.zIndex || 0,
    _old_prompt_: params._old_prompt_,
    _new_prompt_: params._new_prompt_,
    _message_id_: params._message_id_,
  });
  frame.setControlVisible('mtl', false);
  frame.setControlVisible('mtr', false);
  frame.setControlVisible('mbl', false);
  frame.setControlVisible('mbr', false);
  return frame;
};

/**
 * 创建图片元素
 * @param parent 父级元素
 * @param params 图片参数
 * @returns 图片元素
 */
export const createImage = (
  parentId: string,
  params: Partial<ImageProps> & Record<string, any>
): Promise<Group> =>
  new Promise((resolve, reject) => {
    const image = document.createElement('img');
    image.crossOrigin = 'anonymous';
    image.src = params.src;
    image.onload = () => {
      resolve(
        new IImage({
          ...ImageProperties,
          ...params,
          _id_: params._id_ || nanoid(),
          _parent_id_: parentId,
          _shape_name_: params._shape_name_ || '',
          width: params.width || 0,
          height: params.height || 0,
          scaleX: params.scaleX || 1,
          scaleY: params.scaleY || 1,
          flipX: params.flipX || false,
          flipY: params.flipY || false,
          _z_index_: params.zIndex || 0,
          image,
          src: params.src,
        })
      );
    };
    image.onerror = (e) => {
      reject(e);
    };
  });

/**
 * 创建图片元素
 * @param parent 父级元素
 * @param params 图片参数
 * @returns 图片元素
 */
export const createVideo = (
  parentId: string,
  params: Partial<Omit<ImageProps, "src">> & Record<string, any> & { source: string, cover: string }
): Promise<Group> =>
  new Promise((resolve, reject) => {
    const image = document.createElement('img');
    image.crossOrigin = 'anonymous';
    image.src = params.cover;
    image.onload = () => {
      resolve(
        new IVideo({
          ...VideoProperties,
          ...params,
          _id_: params._id_ || nanoid(),
          _shape_name_: params._shape_name_ || '',
          _parent_id_: parentId,
          width: params.width || 0,
          height: params.height || 0,
          scaleX: params.scaleX || 1,
          scaleY: params.scaleY || 1,
          flipX: params.flipX || false,
          flipY: params.flipY || false,
          _z_index_: params.zIndex || 0,
          cover: image,
          source: params.source,
        })
      );
    };
    image.onerror = (e) => {
      reject(e);
    };
  });


/**
 * 创建文本元素
 * @param parent 父级元素
 * @param params 文本参数
 * @returns 文本元素
 */
export const createText = (
  parentId: string,
  params: Partial<TextProps> & Record<string, any>
) => {
  const text = new IText(params.text);
  text.set({
    ...DefaultFontPreset,
    ...params,
    flipX: false,
    flipY: false,
    charSpacing: params.letterSpacing,
    _parent_id_: parentId,
    _id_: params._id_ || nanoid(),
    _loading_: false,
    editable: true,
    _shape_name_: params.text,
    _name_: ElementName.TEXT,
    _font_url_: params._font_url_ || params.fontUrl,
    scaleX: params.scaleX || 1,
    scaleY: params.scaleY || 1,
    _z_index_: params.zIndex || 0,
    _old_prompt_: '',
    _new_prompt_: '',
    _message_id_: '',
  });
  // if (params.flipX !== void 0) {
  //   text.set('flipX', params.flipX)
  // }
  // if (params.flipY !== void 0) {
  //   text.set('flipY', params.flipY)
  // }
  text.on('editing:entered', textEditingEntered.bind(text))
  text.on('editing:exited', textEditingExited.bind(text))
  text.on('mousedblclick', textMouseDoubleClick.bind(text))
  return text;
};


export const createError = (canvas: Canvas, parent: Group, message: string) => {
  const parentWidth = parent.width ?? parent.getBoundingRect().width;
  const parentHeight = parent.height ?? parent.getBoundingRect().height;
  if (!parentWidth || !parentHeight) {
    return null;
  }
  const error = new ErrorElement({
    ...ErrorProperties,
    _id_: nanoid(),
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    width: parent.getScaledWidth(),
    height: parent.getScaledHeight(),
    evented: true,
    selectable: false,
    message,
  });
  error.set({
    _parent_: parent,
  });
  parent.add(error);
  error.set({
    left: 0,
    top: 0,
    angle: 0,
  });
  canvas.renderAll();
  error.setCoords();
  return error;
};

/**
 * 组合元素
 * @param {FabricObject} element
 */
export const createContainerElements = (
  parentId: string,
  elements: FabricObject[],
  params?: Partial<GroupProps> & Record<string, any>
) => {
  // 计算元素的坐标
  const left = Math.min(
    ...elements.map((element) => element.getBoundingRect().left)
  );
  const top = Math.min(
    ...elements.map((element) => element.getBoundingRect().top)
  );
  const width =
    Math.max(
      ...elements.map(
        (element) =>
          element.getBoundingRect().left + element.getBoundingRect().width
      )
    ) - left;
  const height =
    Math.max(
      ...elements.map(
        (element) =>
          element.getBoundingRect().top + element.getBoundingRect().height
      )
    ) - top;
  const options = { ...ContainerProperties, ...params };
  elements.sort((a, b) => a._z_index_ - b._z_index_);
  const group = new Container(elements, {
    ...options,
    _id_: params?._id_ || nanoid(),
    _parent_id_: parentId,
    width: params?.width || width,
    height: params?.height || height,
    left: params?.left || left + (params?.width || width) / 2,
    top: params?.top || top + (params?.height || height) / 2,
    angle: params?.angle || 0,
    flipX: params?.flipX || false,
    flipY: params?.flipY || false,
    scaleX: params?.scaleX || 1,
    scaleY: params?.scaleY || 1,
    _shape_name_: params?._shape_name_ || '',
    _z_index_: params?.zIndex || 0,
    layoutManager: new LayoutManager(new FixedLayout()),
  });

  return group;
};

/**
 * 获取元素的属性
 * @param {FabricObject} element
 */
export function getElementOptions(
  this: Render,
  element: FabricObject,
  options?: GetElementOptionsOptions
): ElementOptions {
  switch (element._name_) {
    case ElementName.TEXT:
      return getTextElementOptions.call(this, element as IText);
    case ElementName.IMAGE:
      return {
        ...getImageOptions.call(
          this,
          (element as Group)
            .getObjects()
            .find((obj) => obj.type === 'image') as FabricImage
        ),
        ...getImageCustomOptions.call(this, element as IImage, options?.maskOptions ?? true),
      };
    case ElementName.FRAME:
      return getFrameElementOptions.call(this, element as Frame);
    case ElementName.CONTAINER:
      return getContainerElementOptions.call(this, element as Container);
    case ElementName.VIDEO:
      return getVideoElementOptions.call(this, element as IVideo, options?.maskOptions ?? true);
    default:
      return {} as ElementOptions;
  }
}

/**
 * 获取图片的属性
 * @param {FabricImage} element
 */
export function getImageOptions(
  this: Render,
  element: FabricImage
): ImageOptions {
  const sourceObject = element.toObject();
  const parent = this.Finder.findById(element._parent_id_);
  return {
    _shape_name_: element.get('_shape_name_'),
    src: sourceObject.src,
    imageWidth: sourceObject.width,
    imageHeight: sourceObject.height,
    left: element.getCenterPoint().x,
    top: element.getCenterPoint().y,
    ...getRelativelyPosition(parent as Group | Canvas, element),
    flipX: element.flipX,
    flipY: element.flipY,
    scaleX: element.scaleX,
    scaleY: element.scaleY,
    angle: element.angle,
    _id_: element._id_,
    opacity: element.opacity,
    visible: element.visible,
    globalCompositeOperation: element.globalCompositeOperation,
    _parent_id_: element._parent_id_,
    _name_: element._name_,
    _loading_: element._loading_,
    _loading_element_: element._loading_element_,
    _message_id_: '',
    _old_prompt_: '',
    _new_prompt_: '',
    _custom_data_history_: element._custom_data_history_,
    _custom_data_: element._custom_data_,
  };
}

/**
 * 获取图片的自定义属性, 基于外层的Group进行获取
 * @param {Group} element
 */
export function getImageCustomOptions(
  this: Render,
  element: IImage,
  maskOptions: boolean
): ImageCustomOptions {
  const parent = this.Finder.findById(element._parent_id_);
  return {
    left: element.getCenterPoint().x,
    top: element.getCenterPoint().y,
    ...getRelativelyPosition(parent as Group | Canvas, element),
    width: element.width,
    height: element.height,
    angle: element.angle,
    ...transformMatrix(element),
    _id_: element._id_,
    opacity: element.opacity,
    visible: element.visible,
    globalCompositeOperation: element.globalCompositeOperation,
    _parent_id_: element._parent_id_,
    _name_: element._name_,
    _shape_name_: element._shape_name_,
    zIndex: (element.parent ?? element.canvas)?._objects?.findIndex((obj) => obj === element),
    _loading_element_: element._loading_element_,
    _loading_: element._loading_,
    _message_id_: '',
    _old_prompt_: '',
    _new_prompt_: '',
    _mask_options_: maskOptions ? getMaskOptions.call(element as Group) : [],
    _custom_data_history_: element._custom_data_history_ ?? {},
    _custom_data_: element._custom_data_ ?? {},
  };
}

/**
 * 获取Frame元素的属性
 * @param element
 * @returns
 */
export function getFrameElementOptions(
  this: Render,
  element: Frame,
): FrameElementOptions {
  const parent = this.Finder.findById(element._parent_id_);
  const children =
    element
      .getObjects()
      .map((obj) => getElementOptions.call(this, obj as Group | IText)) ?? [];
  return {
    _shape_name_: element._shape_name_,
    left: element.getCenterPoint().x,
    top: element.getCenterPoint().y,
    ...getRelativelyPosition(parent as Group | Canvas, element),
    width: element.width,
    height: element.height,
    globalCompositeOperation: element.globalCompositeOperation,
    backgroundColor: element.backgroundColor,
    angle: element.angle,
    ...transformMatrix(element),
    opacity: element.opacity,
    visible: element.visible,
    subTargetCheck: element.subTargetCheck,
    interactive: element.interactive,
    labelText: element.labelText,
    labelFill: element.labelFill,
    labelTextFill: element.labelTextFill,
    labelFontSize: element.labelFontSize,
    _id_: element._id_,
    zIndex: (element.parent ?? element.canvas)?._objects?.findIndex((obj) => obj === element),
    _parent_id_: element._parent_id_,
    _name_: element._name_,
    _loading_: element._loading_,
    _loading_element_: element._loading_element_,
    _message_id_: '',
    _old_prompt_: '',
    _new_prompt_: '',
    _custom_data_history_: element._custom_data_history_ ?? {},
    _custom_data_: element._custom_data_ ?? {},
    children: children as (
      | TextElementOptions
      | (ImageCustomOptions & ImageOptions)
      | ContainerElementOptions
    )[],
  };
}

/**
 * 获取文本元素的属性
 * @param element
 * @returns
 */
export function getTextElementOptions(
  this: Render,
  element: IText
): TextElementOptions {
  const parent = this.Finder.findById(element._parent_id_);
  return {
    _shape_name_: element.text,
    left: element.getCenterPoint().x,
    top: element.getCenterPoint().y,
    ...getRelativelyPosition(parent as Group | Canvas, element),
    width: element.width,
    height: element.height,
    fontFamily: element.fontFamily,
    fontSize: element.fontSize,
    fontWeight: element.fontWeight,
    textAlign: element.textAlign,
    text: element.text,
    fill: element.fill as string,
    angle: element.angle,
    visible: element.visible,
    globalCompositeOperation: element.globalCompositeOperation,
    opacity: element.opacity,
    fontStyle: element.fontStyle,
    linethrough: element.linethrough,
    underline: element.underline,
    lineHeight: element.lineHeight,
    letterSpacing: element.charSpacing,
    stroke: element.stroke as string,
    strokeWidth: element.strokeWidth,
    strokeLineCap: element.strokeLineCap,
    strokeLineJoin: element.strokeLineJoin,
    strokeDashArray: element.strokeDashArray,
    zIndex: (element.parent ?? element.canvas)?._objects?.findIndex((obj) => obj === element),
    ...transformMatrix(element),
    _id_: element._id_,
    _font_url_: element._font_url_,
    _parent_id_: element._parent_id_,
    _name_: element._name_,
    _loading_: element._loading_,
    _loading_element_: element._loading_element_,
    _message_id_: '',
    _old_prompt_: '',
    _new_prompt_: '',
    _custom_data_history_: element._custom_data_history_ ?? {},
    _custom_data_: element._custom_data_ ?? {},
  };
}

export function getContainerElementOptions(
  this: Render,
  element: Container
): ContainerElementOptions {
  const children =
    element
      .getObjects()
      .map((obj) => getElementOptions.call(this, obj as Group | IText)) ?? [];
  const parent = this.Finder.findById(element._parent_id_);
  return {
    _shape_name_: element._shape_name_,
    width: element.width,
    height: element.height,
    left: element.getCenterPoint().x,
    top: element.getCenterPoint().y,
    ...getRelativelyPosition(parent as Group | Canvas, element),
    angle: element.angle,
    ...transformMatrix(element),
    globalCompositeOperation: element.globalCompositeOperation,
    opacity: element.opacity,
    visible: element.visible,
    _id_: element._id_,
    _parent_id_: element._parent_id_,
    _name_: element._name_,
    zIndex: (element.parent ?? element.canvas)?._objects?.findIndex((obj) => obj === element),
    _loading_: element._loading_,
    _loading_element_: element._loading_element_,
    flipX: element.flipX,
    flipY: element.flipY,
    _message_id_: '',
    _old_prompt_: '',
    _new_prompt_: '',
    _custom_data_history_: element._custom_data_history_ ?? {},
    _custom_data_: element._custom_data_ ?? {},
    children: children as (
      | TextElementOptions
      | (ImageCustomOptions & ImageOptions)
    )[],
  };
}

export function getVideoElementOptions(
  this: Render,
  element: IVideo,
  maskOptions: boolean
): z.infer<typeof IVideoOptionsSchema> {
  const parent = this.Finder.findById(element._parent_id_);
  return {
    left: element.getCenterPoint().x,
    top: element.getCenterPoint().y,
    ...getRelativelyPosition(parent as Group | Canvas, element),
    width: element.width,
    height: element.height,
    angle: element.angle,
    ...transformMatrix(element),
    _id_: element._id_,
    opacity: element.opacity,
    visible: element.visible,
    globalCompositeOperation: element.globalCompositeOperation,
    _parent_id_: element._parent_id_,
    _name_: element._name_,
    zIndex: (element.parent ?? element.canvas)?._objects?.findIndex((obj) => obj === element),
    _loading_element_: element._loading_element_,
    _loading_: element._loading_,
    _shape_name_: element._shape_name_,
    _message_id_: '',
    _old_prompt_: '',
    _new_prompt_: '',
    _mask_options_: maskOptions ? (getMaskOptions.call(element as Group)) as z.infer<typeof MaskOptionsGroupSchema>[] : [],
    _custom_data_history_: element._custom_data_history_ ?? {},
    _custom_data_: element._custom_data_ ?? {},
    cover: element.coverSrc,
    source: element.sourceSrc,
    videoWidth: element.width,
    videoHeight: element.height,
  }
}

export const createElement = async (
  options: ElementOptions
): Promise<Group | IText> => {
  if (options._name_ === ElementName.IMAGE)
    return await createImage(options._parent_id_ ?? '', options);
  if (options._name_ === ElementName.TEXT)
    return await createText(options._parent_id_ ?? '', options);
  if (options._name_ === ElementName.VIDEO)
    return await createVideo(options._parent_id_ ?? '', options as z.infer<typeof IVideoOptionsSchema>);
  if (options._name_ === ElementName.FRAME) {
    const children = (options as FrameElementOptions).children ?? [];
    const els = children
    .map(item => ({
      ...item,
      left: item.relativelyLeft,
      top: item.relativelyTop,
    }))
    .map((child) => createElement(child));
    return await Promise.resolve(
      createFrame(options._parent_id_ ?? '', options, await Promise.all(els))
    );
  }
  if (options._name_ === ElementName.CONTAINER) {
    const children = (options as ContainerElementOptions).children ?? [];
    const els = children
    .map(item => ({
      ...item,
      left: item.relativelyLeft,
      top: item.relativelyTop,
    }))
    .map((child) => createElement(child));
    return await Promise.resolve(
      createContainerElements(
        options._parent_id_ ?? '',
        await Promise.all(els),
        options
      )
    );
  }

  throw new Error('未知的元素类型');
};

/**
 * 根据id找到元素
 * @param canvas 画布
 * @param id 元素id
 * @returns 元素
 */
export const findElementById = (
  target: Group | Canvas,
  id: string
): FabricObject | null => {
  Logger.info(
    `findElementById: ${id}, target.children`,
    target?.getObjects()?.length
  );
  const element = target
    .getObjects()
    .filter((obj) => Boolean(obj))
    .find((obj) => obj?._id_ === id);
  if (element) return element;
  const GroupElement = target
    .getObjects()
    .filter((el) => Boolean(el) && isGroup(el));
  for (const el of GroupElement) {
    const result = findElementById(el as Group, id);
    if (result) return result;
  }
  return null;
};

/**
 * 根据id找到自定义的Group
 * @param target 画布
 * @param id 元素id
 * @returns 元素
 */
export const findCustomGroupById = (
  target: Group | Canvas,
  id: string
): Group | Canvas | FabricObject => {
  if (!id) return target;
  const element = target
    .getObjects()
    .filter((obj) => Boolean(obj))
    .find((obj) => obj?._id_ === id) as Group | undefined;
  if (element && isGroup(element)) return element;
  const GroupElement = target
    .getObjects()
    .filter((el) => Boolean(el) && isGroup(el));
  for (const el of GroupElement) {
    const result = findCustomGroupById(el as Group, id);
    if (result && result !== el) return result;
  }
  return target;
};

/**
 * 判断元素是否是Group
 * @param element 元素
 * @returns 是否是Group
 */
export const isGroup = (element: FabricObject): element is Group => {
  return (
    element._name_ === ElementName.FRAME ||
    element._name_ === ElementName.CONTAINER
  );
};

/**
 * 获取元素的坐标
 * @param element 元素
 * @param position 坐标位置 [tl,tr,br,bl]
 * @returns 坐标
 */
export const getCoordsPosition = (element: FabricObject, position: number) => {
  return {
    x: element.getCoords()[position].x,
    y: element.getCoords()[position].y,
  };
};

/**
 * 获取元素最小X的坐标
 * @param element 元素
 * @returns 坐标
 */
export const getMinCoordsX = (element: FabricObject) => {
  return Math.min(...element.getCoords().map((coord) => coord.x));
};

/**
 * 获取元素最大X的坐标
 * @param element 元素
 * @returns 坐标
 */
export const getMaxCoordsX = (element: FabricObject) => {
  return Math.max(...element.getCoords().map((coord) => coord.x));
};

/**
 * 获取元素最小Y的坐标
 * @param element 元素
 * @returns 坐标
 */
export const getMinCoordsY = (element: FabricObject) => {
  return Math.min(...element.getCoords().map((coord) => coord.y));
};

/**
 * 获取元素最大Y的坐标
 * @param element 元素
 * @returns 坐标
 */
export const getMaxCoordsY = (element: FabricObject) => {
  return Math.max(...element.getCoords().map((coord) => coord.y));
};





