import { IText } from "fabric";

export function textEditingEntered(this: IText) {
    this.selectAll();
    if (this.canvas) {
        this.canvas._setActiveObject(this);
        this.canvas.requestRenderAll();
    }
}

export function textEditingExited(this: IText) {
    this.set('editable', false);
    if (this.canvas) {
        this.canvas.renderAll();
    }
}

export function textMouseDoubleClick(this: IText) {
    this.set('editable', true);
    this.enterEditing();
}