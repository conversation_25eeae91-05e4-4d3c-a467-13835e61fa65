import { ElementName } from "../enum";
import { ContainerElementOptions, ElementOptions } from "../types";

export const replaceTextFill = (target: ElementOptions, fill: string) => {
  if (target._name_ === ElementName.TEXT) {
    target = {
        ...target,
        fill: fill,
    }
    return target;
  } else if (target._name_ === ElementName.CONTAINER) {
    (target as ContainerElementOptions).children.forEach((obj) => {
      if (obj._name_ === ElementName.TEXT || obj._name_ === ElementName.CONTAINER) {
        return replaceTextFill(obj as ElementOptions, fill)
      }
    })
    return target;
  }
  return target;
}
