import { ElementOptions } from "../types";
import { nanoid } from 'nanoid';

export function resetId(options: ElementOptions[], parentId?: string) {
    return options.map(item => {
        const newItem = { ...item };
        newItem._id_ = nanoid();
        newItem._parent_id_ = parentId ?? '';
        if ('children' in newItem && Array.isArray(newItem.children)) {
            newItem.children = resetId(newItem.children, newItem._id_);
        }
        return newItem;
    });
}