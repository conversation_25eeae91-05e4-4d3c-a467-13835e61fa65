import { ElementName, MaskOptionsGroup, MaskPathOptions, MaskPolygonOptions, MaskRectOptions, MaskSmartOptions, SmartMask } from "../../index";
import { FabricObject, Group, Path, Polygon, Rect } from "fabric";
import { getRelativelyPosition, transformMatrix } from "../transformMatrix";

/**
 * 获取遮罩选项
 * @param this 
 * @returns 
 */
export function getMaskOptions(this: Group): MaskOptionsGroup[] {
    const maskContainers = this.getObjects().filter(obj => obj.get('isMaskContainer')) as Group[]
    const result = maskContainers.map(maskContainer => {
        return {
            _id_: maskContainer._id_,
            _container_name_: maskContainer.get('maskContainerName'),
            _opacity_: maskContainer.get('opacity'),
            _mask_options_: maskContainer.getObjects().map(obj => {
                return differentiateGraphics.call(obj, maskContainer)
            }).filter(el => el !== null)
        }
    })
    return result

}

// 区分图形
export function differentiateGraphics(this: FabricObject, maskContainer: Group) {
    switch (this._name_) {
        case ElementName.MASK_RECT:
            return getMaskByRect.call(this as Rect, maskContainer)
        case ElementName.MASK_POLYGON:
            return getMaskByPolygon.call(this as Polygon, maskContainer)
        case ElementName.MASK_PATH:
            return getMaskByPath.call(this as Path, maskContainer)
        case ElementName.MASK_SMART:
            return getMaskBySmart.call(this as SmartMask, maskContainer)
        default:
            return null
    }
}
/**
 * 获取矩形遮罩
 * @param this 
 */
export function getMaskByRect(this: Rect, parent: Group): MaskRectOptions {
    return {
        left: this.getCenterPoint().x,
        top: this.getCenterPoint().y,
        ...getRelativelyPosition(parent, this),
        width: this.width,
        height: this.height,
        opacity: this.opacity,
        fill: this.fill as string,
        _shape_name_: this.get('shape_name') || '',
        angle: this.angle,
        visible: this.visible,
        globalCompositeOperation: this.globalCompositeOperation,
        ...transformMatrix(this),
        _id_: this._id_,
        _parent_id_: this._parent_id_,
        _name_: this._name_,
        _loading_: this._loading_,
        _loading_element_: this._loading_element_,
        _message_id_: '',
        _old_prompt_: this._old_prompt_,
        _new_prompt_: this._new_prompt_,
        _custom_data_history_: this._custom_data_history_ ?? {},
        _custom_data_: this._custom_data_ ?? {},
        _parent_name_: this.get('parent_name') || '',
        index: this.get('index') || 0,
        showTitle: this.get('showTitle') || false,
        showCloseBtn: this.get('showCloseBtn') || false,
        erase: this.get('erase') || false,
    }
}


export function getMaskByPolygon(this: Polygon, parent: Group): MaskPolygonOptions {
    return {
        left: this.getCenterPoint().x,
        top: this.getCenterPoint().y,
        points: this.get('points'),
        _shape_name_: this.get('shape_name') || '',
        ...getRelativelyPosition(parent, this),
        fill: this.fill as string,
        angle: this.angle,
        visible: this.visible,
        globalCompositeOperation: this.globalCompositeOperation,
        ...transformMatrix(this),
        _id_: this._id_,
        opacity: this.opacity,
        _parent_id_: this._parent_id_,
        _name_: this._name_,
        _loading_: false,
        _loading_element_: false,
        _message_id_: '',
        _old_prompt_: this._old_prompt_,
        _new_prompt_: this._new_prompt_,
        _custom_data_history_: this._custom_data_history_ ?? {},
        _custom_data_: this._custom_data_ ?? {},
        _parent_name_: this.get('parent_name') || '',
        index: this.get('index') || 0,
        showTitle: this.get('showTitle') || false,
        showCloseBtn: this.get('showCloseBtn') || false,
        erase: this.get('erase') || false,
    }
}

export function getMaskByPath(this: Path, parent: Group): MaskPathOptions {
    return {
        left: this.getCenterPoint().x,
        top: this.getCenterPoint().y,
        path: this.get('path'),
        ...getRelativelyPosition(parent, this),
        stroke: this.stroke as string,
        _shape_name_: this.get('shape_name') || '',
        strokeWidth: this.strokeWidth,
        opacity: this.opacity,
        angle: this.angle,
        visible: this.visible,
        globalCompositeOperation: this.globalCompositeOperation,
        ...transformMatrix(this),
        strokeLineCap: this.strokeLineCap,
        strokeMiterLimit: this.strokeMiterLimit,
        strokeLineJoin: this.strokeLineJoin,
        strokeDashArray: this.strokeDashArray || [],
        _id_: this._id_,
        _parent_id_: this._parent_id_,
        _name_: this._name_,
        _loading_: false,
        _loading_element_: false,
        _message_id_: '',
        _old_prompt_: this._old_prompt_,
        _new_prompt_: this._new_prompt_,
        _custom_data_history_: this._custom_data_history_ ?? {},
        _custom_data_: this._custom_data_ ?? {},
        _parent_name_: this.get('parent_name') || '',
        index: this.get('index') || 0,
        showTitle: this.get('showTitle') || false,
        showCloseBtn: this.get('showCloseBtn') || false,
        erase: this.get('erase') || false,
    }
}


export function getMaskBySmart(this: SmartMask, parent: Group): MaskSmartOptions {
    return {
        left: this.getCenterPoint().x,
        top: this.getCenterPoint().y,
        ...getRelativelyPosition(parent, this),
        width: this.width,
        height: this.height,
        img: this.image,
        _shape_name_: this.get('shape_name') || '',
        opacity: this.opacity,
        revertImage: this.revertImage,
        fill: this.fill as string,
        angle: this.angle,
        visible: this.visible,
        globalCompositeOperation: this.globalCompositeOperation,
        ...transformMatrix(this),
        _id_: this._id_,
        _parent_id_: this._parent_id_,
        _name_: this._name_,
        _loading_: this._loading_,
        _loading_element_: this._loading_element_,
        _message_id_: '',
        _old_prompt_: this._old_prompt_,
        _new_prompt_: this._new_prompt_,
        _custom_data_history_: this._custom_data_history_ ?? {},
        _custom_data_: this._custom_data_ ?? {},
        _parent_name_: this.get('parent_name') || '',
        index: this.get('index') || 0,
        showTitle: this.get('showTitle') || false,
        showCloseBtn: this.get('showCloseBtn') || false,
        erase: this.get('erase') || false,
    }
}

/**
 * 创建遮罩
 * @param this 
 * @param options 
 * @returns 
 */
export function createMask(
    this: Group,
    options: MaskRectOptions | MaskPolygonOptions | MaskPathOptions
): FabricObject {
    let maskObject: FabricObject
    switch (options._name_) {
        case ElementName.MASK_RECT:
            maskObject = createRectMask(options as MaskRectOptions)
            this.add(maskObject)
            this.canvas?.fire('mask:rect:created', { target: maskObject , element: this.parent as FabricObject})
            break
        case ElementName.MASK_POLYGON:
            maskObject = createPolygonMask(options as MaskPolygonOptions)
            this.add(maskObject)
            this.canvas?.fire('mask:lasso:created', { target: maskObject , element: this.parent as FabricObject})
            break
        case ElementName.MASK_PATH:
            maskObject = createPathMask(options as MaskPathOptions)
            this.add(maskObject)
            this.canvas?.fire('mask:path:created', { target: maskObject , element: this.parent as FabricObject})
            break
        case ElementName.MASK_SMART:
            maskObject = createSmartMask(options as MaskSmartOptions)
            this.add(maskObject)
            this.canvas?.fire('mask:smart:created', { target: maskObject , element: this.parent as FabricObject})
            break
        default:
            throw new Error(`Unknown mask type: ${options._name_}`)
    }
    maskObject.set({
        left: options.relativelyLeft,
        top: options.relativelyTop,
    })
    this.canvas?.renderAll()
    return maskObject
}


export function createRectMask(options: MaskRectOptions) {
    const target = new Rect({
        left: options.left,
        top: options.top,
        width: options.width,
        height: options.height,
        globalCompositeOperation: options.globalCompositeOperation,
        fill: options.fill,
      })
      target.set({
        _id_: options._id_,
        _name_: ElementName.MASK_RECT,
        _parent_id_: options._parent_id_,
        _parent_name_: options._parent_name_,
        _old_prompt_: options._old_prompt_,
        _new_prompt_: options._new_prompt_,
        _message_id_: options._message_id_,
        _loading_element_: false,
        _custom_data_history_: options._custom_data_history_,
        _custom_data_: options._custom_data_,
        index: options.index,
        showTitle: options.showTitle,
        showCloseBtn: options.showCloseBtn,
        erase: options.erase,
        isMask: true,
        maskType: 'rect'
      })
      return target
}


export function createPolygonMask(options: MaskPolygonOptions) {
    const target = new Polygon(options.points, {
        fill: options.fill,
        globalCompositeOperation: options.globalCompositeOperation,
        strokeLineCap: 'round',
        strokeLineJoin: 'round',
      })
      target.set({
        _id_: options._id_,
        _name_: ElementName.MASK_POLYGON,
        _parent_id_: options._parent_id_,
        _parent_name_: options._parent_name_,
        _old_prompt_: options._old_prompt_,
        _new_prompt_: options._new_prompt_,
        _message_id_: options._message_id_,
        _loading_element_: false,
        _custom_data_history_: options._custom_data_history_,
        _custom_data_: options._custom_data_,
        isMask: true,
        maskType: 'polygon',
        index: options.index
      })
    return target
}


export function createPathMask(options: MaskPathOptions) {
    const target = new Path(options.path, {
        fill: null,
        stroke: options.stroke,
        strokeWidth: options.strokeWidth,
        strokeLineCap: options.strokeLineCap,
        strokeMiterLimit: options.strokeMiterLimit,
        strokeLineJoin: options.strokeLineJoin,
        strokeDashArray: options.strokeDashArray,
      });
      target.set({
        _id_: options._id_,
        _name_: ElementName.MASK_PATH,
        _parent_id_: options._parent_id_,
        _parent_name_: options._parent_name_,
        _old_prompt_: options._old_prompt_,
        _new_prompt_: options._new_prompt_,
        _message_id_: options._message_id_,
        _loading_element_: false,
        _custom_data_history_: options._custom_data_history_,
        _custom_data_: options._custom_data_,
        index: options.index,
        isMask: true,
        maskType: 'path',
        globalCompositeOperation: options.globalCompositeOperation,
    })
    return target
}

export function createSmartMask(options: MaskSmartOptions) {
    const target = new SmartMask({
        revertImage: options.revertImage,
        img: options.img,
        width: options.width,
        angle: options.angle,
        left: options.left,
        top: options.top,
        height: options.height,
        globalCompositeOperation: options.globalCompositeOperation,
        fill: options.fill,
      })
      target.set({
        _id_: options._id_,
        _name_: ElementName.MASK_RECT,
        _parent_id_: options._parent_id_,
        _parent_name_: options._parent_name_,
        _old_prompt_: options._old_prompt_,
        _new_prompt_: options._new_prompt_,
        _message_id_: options._message_id_,
        _loading_element_: false,
        _custom_data_history_: options._custom_data_history_,
        _custom_data_: options._custom_data_,
        index: options.index,
        showTitle: options.showTitle,
        showCloseBtn: options.showCloseBtn,
        erase: options.erase,
        isMask: true,
        maskType: 'smart'
      })
      return target
}