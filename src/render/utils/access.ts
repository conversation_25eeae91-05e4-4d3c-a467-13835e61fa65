/**
 * Converts an SVG string to a Base64-encoded Data URI.
 * @param svg - The SVG string to encode.
 * @returns A Base64-encoded Data URI.
 */
export function svgToBase64(svg: string): string {
    // Remove whitespace from SVG for compact representation
    const cleanedSvg = svg
        .replace(/>\s+</g, '><') // Remove whitespace between tags
        .trim(); // Remove leading and trailing whitespace

    // Encode the SVG string to Base64
    const base64 = btoa(unescape(encodeURIComponent(cleanedSvg)));

    // Return as a data URI
    return `data:image/svg+xml;base64,${base64}`;
}