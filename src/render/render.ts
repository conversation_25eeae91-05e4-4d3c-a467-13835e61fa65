import "./base/ownDefaults";
import { getContainer } from "./utils/dom";
import { Wheel } from "./base/wheel";
import { Actions } from "./base/actions";
import { version } from "../../package.json";
import {
  centerSelectedObject,
  getAllImageElements,
  getViewPortVisibleArea,
  isObjectInArea,
  showAllObjects,
  sortElementsByZIndex,
} from "./utils/canvas";
import { Plugin } from "./base/types";
import { Railings } from "./base/railings";
import {
  FabricObject,
  Group,
  Canvas,
  IText,
  CanvasEvents, InteractiveFabricObject,
} from "fabric";
import {
  createElement,
  createFrame,
  ElementName,
  findFontByData,
  getElementOptions,
  loadFont,
  WarningType,
} from "./utils";
import {
  ContainerElementOptions,
  ElementOptions,
  FrameElementOptions,
  FrameElementParams,
} from "./utils/types";
import { defaultMaxZoom, defaultMinZoom, defaultWarningImageCount } from "./base/ownDefaults";
import Logger from "./logger";
import { Finder } from "./base/finder";
import { LabelTagMouse } from "./base/labelTagMouse";
import { CoreOptions } from "./render.type";


export class Render {
  private _canvas!: HTMLCanvasElement;
  private _container!: {
    element: HTMLElement;
    width: number;
    height: number;
  };
  private _plugins: Map<string, Plugin> = new Map();
  public _FC!: Canvas;
  private _ResizeObserver!: ResizeObserver;
  private _Wheel: Wheel;
  public _Railings: Railings;
  public Actions: Actions;
  public _maxDepth: number;
  public debug: boolean = false;
  public LabelTagMouse: LabelTagMouse;
  public logger: Logger = new Logger(this);
  public Finder: Finder = new Finder(this);
  private _events: Partial<
    Record<
      keyof CanvasEvents,
      Array<(e: CanvasEvents[keyof CanvasEvents]) => void>
    >
  > = {};
  constructor(private options: CoreOptions) {
    this._maxDepth = this.options.maxImageCount ?? defaultWarningImageCount;
    this.options.minZoom = this.options.minZoom ?? defaultMinZoom;
    this.options.maxZoom = this.options.maxZoom ?? defaultMaxZoom;
    this.init();
    this._Wheel = new Wheel(this._FC, {
      minZoom: this.options.minZoom,
      maxZoom: this.options.maxZoom,
    });
    this._FC.set("_minZoom", this.options.minZoom);
    this._FC.set("_maxZoom", this.options.maxZoom);
    this.LabelTagMouse = new LabelTagMouse(this._FC);
    this._Railings = new Railings(this, this.options.showRailing);
    this.Actions = new Actions(this);
    this.debug = Boolean(localStorage.getItem("whee-infinite-canvas:debug"));
    this._bindEvents();
    this.logger.info(`version: ${version}`);
  }
  private init = () => {
    this._canvas = document.createElement("canvas");
    this._canvas.style.position = "absolute";
    this._canvas.style.top = "0";
    this._canvas.style.left = "0";
    this._container = getContainer(this.options.container);
    this._container.element.style.position = "relative";
    this._container.element.appendChild(this._canvas);

    this._FC = new Canvas(this._canvas, {
      backgroundColor: "rgba(0, 0, 0, 0.1)",
      interactive: true,
      isDrawingMode: false,
      enableRetinaScaling: true,
      selectionKey: this.options.selectionKey,
      uniScaleKey: null,
    });
    this.setCanvasSize();
    this.initResizeObserver();
    InteractiveFabricObject.ownDefaults = {
      ...InteractiveFabricObject.ownDefaults,
      lockScalingFlip: this.options.lockScalingFlip ?? true,
    };
  };
  private _bindEvents = () => {
    this._FC.on("object:added", this.handleObjectAdded);
    Object.entries(this._events).forEach(([event, handlers]) =>
      handlers.forEach((handler) =>
        this._FC.on(event as keyof CanvasEvents, handler)
      )
    );
  };
  private _unbindEvents = () => {
    this._FC.off("object:added", this.handleObjectAdded);
    Object.entries(this._events).forEach(([event, handlers]) =>
      handlers.forEach((handler) =>
        this._FC.off(event as keyof CanvasEvents, handler)
      )
    );
  };
  private setCanvasSize = () => {
    this._container = getContainer(this.options.container);
    this._FC.setDimensions({
      width: this._container.width,
      height: this._container.height,
    });
    this._FC.renderAll();
  };

  private initResizeObserver = () => {
    this._ResizeObserver = new ResizeObserver(() => {
      this.setCanvasSize();
    });
    this._ResizeObserver.observe(this._container.element);
  };
  /**
   * 锁定画布元素
   */
  _lockAll = () => {
    this._FC.discardActiveObject();
    this._FC.set({
      selection: false,
    });
    this._FC.forEachObject((obj) => {
      obj.selectable = false;
      obj.evented = false;
    });
  };
  /**
   * 解锁画布元素
   */
  _unlockAll = () => {
    this._FC.set({
      selection: true,
    });
    this._FC.forEachObject((obj) => {
      obj.selectable = true;
      obj.evented = true;
    });
  };

  public unmount = () => {
    this._plugins.forEach((plugin) => {
      plugin.__destroy__();
    });
    this._unbindEvents();
    this._Wheel._destroy();
    this._Railings._destroy();
    this.Actions._destroy();
    this.LabelTagMouse._destroy();
    this._plugins.clear();
    this._ResizeObserver.disconnect();
    this._FC.dispose();
  };

  public add = (...elements: FabricObject[]) => {
    this._FC.add(...elements);
    this._FC.requestRenderAll();
    elements.forEach((el) => el.setCoords());
  };
  /**
   * 将元素添加到视口中心
   * @param elements 元素
   */
  public addToViewPortCenter = (...elements: FabricObject[]) => {
    const { centerX, centerY } = getViewPortVisibleArea(this._FC);
    elements.forEach((el) => {
      el.set({
        left: centerX,
        top: centerY,
      });
    });
    this._FC.add(...elements);
    this._FC.setActiveObject(elements[elements.length - 1]);
    this._FC.requestRenderAll();
  };

  public addToViewPortCenterByArrangement = (
    element: FabricObject,
    offsetX: number = 10,
    offsetY: number = 0
  ) => {
    const width = element.getScaledWidth() + offsetX;
    const height = element.getScaledHeight() + offsetY;
    const area = {
      x: 0,
      y: 0,
      width,
      height,
    };
    const selected = this._FC.getActiveObject();
    const { centerX, centerY } = getViewPortVisibleArea(this._FC);
    area.x = centerX - width / 2;
    area.y = centerY - height / 2;

    if (selected) {
      area.x = selected.left - width / 2;
      area.y = selected.top - height / 2;
    }

    while (isObjectInArea.call(this._FC, area, false)) {
      area.x += area.width;
    }

    element.set({
      left: area.x + area.width / 2,
      top: area.y + area.height / 2,
    });
    element.setCoords();
    this._FC.add(element);
    this._FC.requestRenderAll();
    this.logger.info(`
      append element to addToViewPortCenterByArrangement
      element.left: ${element.left},
      element.top: ${element.top},
      element.width: ${element.width},
      element.height: ${element.height}`);
  };


  /**
   * 使用插件
   * @param plugin 插件
   */
  public use = (plugin: Plugin) => {
    const name = plugin.__name__;
    if (this._plugins.has(name)) {
      this._plugins.get(name)?.__destroy__();
    }
    this._plugins.set(name, plugin);
    return this;
  };
  /**
   * 卸载插件
   * @param name 插件名称
   */
  public unUse = (name: string) => {
    this._plugins.get(name)?.__destroy__();
    this._plugins.delete(name);
  };
  /**
   *  定位元素至画布中心
   * @param options 选项
   * @param options.target 目标元素
   * @param options.duration 动画持续时间(ms)
   */
  public backToOriginPosition = (options?: {
    target?: FabricObject;
    duration?: number;
    scaleFactor?: number;
  }) => {
    const { target, duration, scaleFactor } = options ?? {};
    const activeObject = target ?? this._FC.getActiveObject();
    if (activeObject) {
      centerSelectedObject(this._FC, activeObject, duration, scaleFactor ?? 0.8);
    } else {
      const lastObject = this._FC.item(this._FC.size() - 1);
      if (lastObject) {
        centerSelectedObject(this._FC, lastObject, duration, scaleFactor ?? 0.8);
      }
    }
  };

  /**
   *  定位元素至画布中心
   * @param duration 动画持续时间(ms)
   */
  public zoomToFitCanvas = (
    limitScale?: number,
    leftPanelWidth?: number,
    rightPanelWidth?: number,
    topPanelHeight?: number,
    bottomPanelHeight?: number,
    duration?: number
  ) => {
    showAllObjects(
      this._FC,
      [],
      limitScale,
      leftPanelWidth,
      rightPanelWidth,
      topPanelHeight,
      bottomPanelHeight,
      duration
    );
  };

  /**
   *  定位对象到画布中心
   * @param duration 动画持续时间(ms)
   */
  public focusObjects = (
    targets: Array<FabricObject>,
    limitScale?: number,
    leftPanelWidth?: number,
    rightPanelWidth?: number,
    topPanelHeight?: number,
    bottomPanelHeight?: number,
    duration?: number
  ) => {

    if (!targets.length) {
      return;
    }

    showAllObjects(
      this._FC,
      targets,
      limitScale,
      leftPanelWidth,
      rightPanelWidth,
      topPanelHeight,
      bottomPanelHeight,
      duration
    );
  };
  /**
   * 锁定画布并监听移动
   */
  public lockAllAndListenMove = () => {
    this._lockAll();
    this._Wheel.listenLockMove();
  };
  /**
   * 解锁画布并取消监听移动
   */
  public unlockAllAndUnListenMove = () => {
    this._unlockAll();
    this._Wheel.unListenLockMove();
  };

  /**
   * 设置画布缩放
   * 基于屏幕视口中心缩放
   * @param zoom 缩放比例
   */
  public setZoom = (zoom: number) => {
    this._Wheel.setZoomByViewPortCenter(zoom);
  };

  /**
   * 获取场景数据
   * @returns 场景数据
   */
  public getSceneData = () => {
    const els = this._FC
      .getObjects()
      .filter(
        (el) =>
          el._name_ === ElementName.FRAME ||
          el._name_ === ElementName.CONTAINER ||
          el._name_ === ElementName.IMAGE ||
          el._name_ === ElementName.TEXT ||
          el._name_ === ElementName.VIDEO
      );
    return this.setElementZIndex(
      els.map((el) => getElementOptions.call(this, el as Group | IText)),
      this._FC
    );
  };
  /**
   * 设置元素的zIndex
   * @param elements 元素
   * @returns 元素
   */
  public setElementZIndex = (
    elements: Array<ElementOptions>,
    parent: Group | Canvas
  ) => {
    elements.forEach((element) => {
      const zIndex = parent
        .getObjects()
        .findIndex((obj) => obj._id_ === element._id_);
      element.zIndex = zIndex;
      if (
        element._name_ === ElementName.CONTAINER ||
        element._name_ === ElementName.FRAME
      ) {
        const children =
          (element as ContainerElementOptions | FrameElementOptions)
            ?.children ?? [];
        this.setElementZIndex(
          children,
          parent.getObjects().find((obj) => obj._id_ === element._id_) as Group
        );
      }
    });
    return elements;
  };

  public setOptionsZIndex = (options: Array<ElementOptions>) => {
    options.forEach((option) => {
      const parent = this.Finder.findById(option._parent_id_);
      const zIndex = (parent as Group)
        .getObjects()
        .findIndex((obj) => obj._id_ === option._id_);
      option.zIndex = zIndex;
      if (
        option._name_ === ElementName.CONTAINER ||
        option._name_ === ElementName.FRAME
      ) {
        const children =
          (option as ContainerElementOptions | FrameElementOptions)?.children ??
          [];
        this.setOptionsZIndex(children);
      }
    });
    return options;
  };

  /**
   * 获取场景数据
   * @returns 场景数据
   */
  public getTemplateSceneData = () => {
    const els = this._FC.getActiveObjects();
    return els.map((el) =>
      getElementOptions.call(this, el as Group | IText)
    )[0];
  };

  /**
   * 设置场景数据
   * @param data 场景数据
   */
  public setSceneData = async (data: Array<ElementOptions>) => {
    this.logger.info(`setSceneData: `, data);
    const fonts = findFontByData(data);
    this.logger.info(`fonts: `, fonts);
    await loadFont(fonts);
    data = sortElementsByZIndex(data);
    const els = await Promise.allSettled(
      data.map((options) => createElement(options))
    );
    const elements = els
      .filter((el) => el.status === "fulfilled")
      .map((el) => el.value);
    const errors = els
      .filter((el) => el.status === "rejected")
      .map((el) => el.reason);
    if (errors.length > 0) {
      this.logger.error(`setSceneData: `, errors);
    }
    this._FC.add(...elements);
    this._FC.setActiveObject(elements[elements.length - 1]);
    this._FC.renderAll();
    return elements;
  };
  /**
   * 获取屏幕位置
   * @returns 屏幕位置
   */
  public getScreenPosition = () => {
    const viewPortArea = getViewPortVisibleArea(this._FC);
    const centerX = (viewPortArea.left + viewPortArea.right) / 2;
    const centerY = (viewPortArea.top + viewPortArea.bottom) / 2;
    return { x: centerX, y: centerY };
  };

  /**
   * 添加模板
   * @param params 模板参数
   * @param position 模板位置
   */
  public addFrame = async (
    params: FrameElementParams,
    position?: { x: number; y: number }
  ) => {
    const viewPortArea = getViewPortVisibleArea(this._FC);
    const centerX = (viewPortArea.left + viewPortArea.right) / 2;
    const centerY = (viewPortArea.top + viewPortArea.bottom) / 2;
    const point = position ?? {
      x: centerX,
      y: centerY,
    };
    params.left = point.x;
    params.top = point.y;
    params.children = (params.children ?? []).map((child, index) => ({
      ...child,
      left: (child.left ?? 0) + point.x,
      top: (child.top ?? 0) + point.y,
      zIndex: index,
    }));
    const children = params.children as ElementOptions[];
    const fonts = findFontByData(params.children);
    delete params.children;
    const frame = createFrame("", params);
    // this.Actions.setLoading({
    //   id: frame._id_,
    // });
    this._FC.renderAll();
    await loadFont(fonts);
    this.logger.info(`fonts: `, fonts);
    const elements = await Promise.all(
      children.map((child) =>
        createElement({ ...child, _parent_id_: frame._id_ })
      )
    );
    frame.add(...elements);
    this.Actions.setLoaded(frame._id_);
    this.addToViewPortCenterByArrangement(frame);
    this._FC.renderAll();
    return frame;
  };

  /**
   * 清除画布
   */
  public clear = () => {
    this._FC.clear();
    this._FC.requestRenderAll();
    this._FC.discardActiveObject();
    this._FC.set({
      backgroundColor: "rgba(0, 0, 0, 0.1)",
    });
  };

  private handleObjectAdded = ({ target }: { target: FabricObject }) => {
    const images = getAllImageElements.call(this);
    this.logger.info(`画布图片数量: `, images);
    const warningImageCount =
      this.options.maxImageCount ?? defaultWarningImageCount;
    this.logger.info(`画布图片数量: ${images.length}/${warningImageCount}`);
    if (images.length > warningImageCount) {
      this.logger.warn(`画布中图片数量超过${warningImageCount}，请注意优化`);
      this._FC.fire("warning", {
        objects: images,
        target,
        type: WarningType.IMAGE_COUNT,
      });
    }
  };

  /**
   * 判断当前元素是否在画布可视化区域
   * @param element 元素
   * @returns 是否在画布可视化区域
   */
  public isElementInViewport = (
    element: FabricObject,
    isAllowPartialInViewport: boolean = false
  ) => {
    const viewPortArea = getViewPortVisibleArea(this._FC);
    const elementBounds = element.getBoundingRect();
    if (isAllowPartialInViewport) {
      return !(
        elementBounds.left + elementBounds.width < viewPortArea.left ||
        elementBounds.left > viewPortArea.right ||
        elementBounds.top + elementBounds.height < viewPortArea.top ||
        elementBounds.top > viewPortArea.bottom
      );
    }
    return (
      elementBounds.left >= viewPortArea.left &&
      elementBounds.left + elementBounds.width <= viewPortArea.right &&
      elementBounds.top >= viewPortArea.top &&
      elementBounds.top + elementBounds.height <= viewPortArea.bottom
    );
  };
}
