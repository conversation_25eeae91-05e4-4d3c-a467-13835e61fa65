import * as fabric from 'fabric';
import { HistoryChangeType } from './plugins/renderHistory';
import { LoadingType } from './utils';
import { IBaseBrush } from './base/package/brush';


type ReplenishmentFabricObject = {
  erasable: boolean | 'deep';
  labelText: string;
  labelFill: string;
  labelTextFill: string;
  labelFontSize: number;
  _id_: string;
  _name_: string;
  _loading_: boolean;
  _loading_text_: string;
  _loading_type_: LoadingType;
  _parent_id_: string;
  _old_prompt_: string;
  _new_prompt_: string;
  _message_id_: string;
  _loading_element_: boolean;
  _custom_data_history_: Record<string, any>;
  _custom_data_: Record<string, any>;
  _z_index_: number;
  isMask: boolean | undefined
};
declare module 'fabric' {
  export interface FabricObject
    extends ReplenishmentFabricObject,
      FabricObjectPropsOrigin {}
    export interface IText {
      _font_url_: string;
    }
    export interface Canvas {
      freeDrawingBrush: IBaseBrush | undefined
    }

  export interface FabricObjectProps
    extends ReplenishmentFabricObject,
      FabricObjectPropsOrigin {}

  export interface SerializedObjectProps extends ReplenishmentFabricObject {
    type: string;
  }
  export interface Control {
    isTransparent: boolean;
  }
  export interface CanvasEvents {
    'object:delete': { objects: FabricObject[]; target?: FabricObject };
    'object:insert': { objects: FabricObject[]; target?: FabricObject };
    'object:changed': { objects?: FabricObject[]; target?: FabricObject };
    'warning': { objects?: FabricObject[]; target?: FabricObject, type: number };
    'viewport:translate': { delta: Point, left: number, top: number };
    'viewport:zoom': { zoom: number };
    'history:changed': { type: HistoryChangeType };
    'mask:rect:before': { target: FabricObject, element: FabricObject };
    'mask:rect:created': { target: FabricObject, element: FabricObject };
    'mask:path:before': { target: FabricObject, element: FabricObject };
    'mask:path:created': { target: FabricObject, element: FabricObject };
    'mask:lasso:before': { target: FabricObject, element: FabricObject };
    'mask:lasso:created': { target: FabricObject, element: FabricObject };
    'mask:smart:created': { target: FabricObject, element: FabricObject  };
    'mask:rect:deleted': { target: FabricObject, element: FabricObject };
    'mask:path:deleted': { target: FabricObject, element: FabricObject };
    'mask:lasso:deleted': { target: FabricObject, element: FabricObject };
    'mask:smart:deleted': { target: FabricObject, element: FabricObject };
  }

  export interface TPointerEventInfo<E extends TPointerEvent> {
    objects?: FabricObject[];
  }
}

// 确保 `fabric` 命名空间的导出
export declare namespace fabric {
  export * from 'fabric';
}

declare module 'svg64' {
  /**
   * Converts an SVG string to a Base64-encoded Data URI.
   * @param svg - The SVG string.
   * @returns A Base64-encoded Data URI.
   */
  export default function svg64(svg: string): string;
}
// 避免模块污染
export {};
