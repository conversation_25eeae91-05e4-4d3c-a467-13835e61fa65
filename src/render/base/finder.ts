import { Canvas, Group, FabricObject } from 'fabric';
import { Render } from '../render';
import { isRealElement } from '../utils';

type FabricElement = Group | FabricObject;

export class Finder {
  constructor(private render: Render) {}
  /**
   * 通过id查找元素
   * @param id 元素id
   * @param target 查找目标
   * @returns 元素
   */
  public findById(
    id: string,
    target: Group | Canvas = this.render._FC
  ): FabricElement | Canvas {
    if (!target || !id) return this.render._FC;
    const objects = target.getObjects();
    if (!Array.isArray(objects)) return this.render._FC;

    const element = objects.find((obj) => obj?._id_ === id);
    if (element) return element;

    for (const obj of objects) {
      if (obj instanceof Group) {
        const found = this.findById(id, obj);
        if (found && !(found instanceof Canvas)) return found;
      }
    }
    return this.render._FC;
  }

  /**
   * 通过自定义条件查找元素
   */
  public findByCondition(
    condition: (obj: FabricElement) => boolean,
    target: Group | Canvas = this.render._FC
  ): FabricElement | null {
    if (!target || typeof condition !== 'function') return null;

    const objects = target.getObjects();
    if (!Array.isArray(objects)) return null;

    // 检查当前层级
    const element = objects.find((obj) => obj && condition(obj));
    if (element) return element;

    for (const obj of objects) {
      if (obj instanceof Group) {
        const found = this.findByCondition(condition, obj);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 查找所有匹配条件的元素
   */
  public findAll(
    condition: (obj: FabricElement) => boolean,
    target: Group | Canvas = this.render._FC
  ): FabricElement[] {
    if (!target || typeof condition !== 'function') return [];

    const results: FabricElement[] = [];
    const objects = target.getObjects();

    if (!Array.isArray(objects)) return results;

    objects.forEach((obj) => {
      if (obj && condition(obj)) {
        results.push(obj);
      }
      if (obj instanceof Group) {
        results.push(...this.findAll(condition, obj));
      }
    });

    return results;
  }

  public findRealElement(target: FabricObject): FabricObject | undefined {
    if (isRealElement(target)) return target
    if (!target.parent) return
    while(target?.parent) {
      if (isRealElement(target.parent)) return target.parent
      target = target.parent
    }
  }





  /**
   * 查找元素的父组
   */
  static findParentGroup(
    target: Canvas | Group,
    childId: string
  ): Group | null {
    if (!target || !childId) return null;

    const objects = target.getObjects();
    if (!Array.isArray(objects)) return null;

    if (objects.some((obj) => obj?._id_ === childId)) {
      return target instanceof Group ? target : null;
    }

    for (const obj of objects) {
      if (obj instanceof Group) {
        const parent = this.findParentGroup(obj, childId);
        if (parent) return parent;
      }
    }

    return null;
  }

  /**
   * 获取元素的完整路径
   */
  public getElementPath(
    id: string,
    target: Group | Canvas = this.render._FC
  ): string[] {
    const path: string[] = [];
    const findPath = (
      current: Canvas | Group,
      searchId: string,
      currentPath: string[]
    ): boolean => {
      const objects = current.getObjects();

      for (const obj of objects) {
        if (!obj) continue;

        if (obj?._id_ === searchId) {
          currentPath.push(obj._id_);
          return true;
        }

        if (obj instanceof Group) {
          currentPath.push(obj._id_ || 'unnamed-group');
          if (findPath(obj, searchId, currentPath)) {
            return true;
          }
          currentPath.pop();
        }
      }

      return false;
    };

    findPath(target, id, path);
    return path;
  }
}
