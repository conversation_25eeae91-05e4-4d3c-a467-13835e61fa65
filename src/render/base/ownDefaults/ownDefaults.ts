import {
  Canvas,
  Control,
  ControlActionHandler,
  controlsUtils,
  FabricObject,
  InteractiveFabricObject,
} from "fabric";
import {
  Cursor<PERSON>tyle<PERSON>and<PERSON>,
  MouseStyle,
} from "../../plugins/renderStyle/types";
import { ControlRenderingStyleOverride } from "node_modules/fabric/dist/src/controls/controlRendering";
import { ElementName } from "../../utils";
import {
  defaultSelectionColor,
  getSelectionColor,
} from "../theme/selectionColor";

interface Cursor {
  move: string;
  default: string;
  hover: string;
}
// 默认的鼠标样式
export const CanvasDefaultCursor = `url("data:image/svg+xml;base64,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") 7 7, auto`;
// =========================缩放控制器==============================
// 左上角缩放控制器原SVG
export const ScaleTopLeftCursorBySourceHandler = (angle: number = 0) => {
  return `
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g transform="rotate(${angle}, 16, 16)">
        <g filter="url(#filter0_d_22197_203027)">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M18.1573 20.2402L11.2593 13.3423L9.1539 15.4477C9.0468 15.5548 8.86336 15.4889 8.849 15.3381L8.23914 8.93449C8.22847 8.82254 8.32243 8.72858 8.43439 8.73924L14.838 9.34911C14.9888 9.36347 15.0547 9.5469 14.9476 9.65401L12.8422 11.7594L19.7401 18.6574L21.8456 16.5519C21.9527 16.4448 22.1361 16.5108 22.1505 16.6616L22.7603 23.0652C22.771 23.1771 22.677 23.2711 22.5651 23.2604L16.1615 22.6506C16.0107 22.6362 15.9447 22.4528 16.0518 22.3457L18.1573 20.2402Z" fill="#1A1A1A"/>
        </g>
        <g filter="url(#filter1_d_22197_203027)">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.2594 13.3423L18.1574 20.2402L16.0519 22.3456C15.9448 22.4527 16.0108 22.6362 16.1616 22.6505L22.5652 23.2604C22.6771 23.2711 22.7711 23.1771 22.7604 23.0652L22.1506 16.6616C22.1362 16.5108 21.9528 16.4448 21.8456 16.5519L19.7402 18.6573L12.8423 11.7594L14.9477 9.65398C15.0548 9.54687 14.9889 9.36344 14.8381 9.34908L8.43447 8.73921C8.32252 8.72855 8.22856 8.82251 8.23922 8.93446L8.84909 15.3381C8.86345 15.4888 9.04688 15.5548 9.15399 15.4477L11.2594 13.3423ZM14.1086 11.7594L15.5809 10.2871C16.2235 9.64449 15.8277 8.54387 14.923 8.45771L8.51937 7.84784C7.84764 7.78387 7.28388 8.34763 7.34786 9.01935L7.95772 15.4229C8.04389 16.3277 9.1445 16.7235 9.78713 16.0808L11.2594 14.6086L16.8911 20.2402L15.4188 21.7125C14.7761 22.3551 15.1719 23.4557 16.0767 23.5419L22.4803 24.1518C23.152 24.2157 23.7158 23.652 23.6518 22.9803L23.0419 16.5767C22.9558 15.6719 21.8551 15.2761 21.2125 15.9188L19.7402 17.3911L14.1086 11.7594Z" fill="white"/>
        </g>
        <defs>
          <filter id="filter0_d_22197_203027" x="5.83828" y="7.53842" width="19.3234" height="19.3228" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.2"/>
            <feGaussianBlur stdDeviation="1.2"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_22197_203027"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_22197_203027" result="shape"/>
          </filter>
          <filter id="filter1_d_22197_203027" x="4.94375" y="6.64288" width="21.1125" height="21.1139" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.2"/>
            <feGaussianBlur stdDeviation="1.2"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_22197_203027"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_22197_203027" result="shape"/>
          </filter>
        </defs>
      </g>
    </svg>
  `;
};
// 右上角缩放控制器原SVG
export const ScaleTopRightCursorBySourceHandler = (angle: number = 0) => {
  return `
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g transform="rotate(${angle}, 16, 16)">
        <g filter="url(#filter0_d_20847_244257)">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M12.8427 20.2402L19.7407 13.3423L21.8461 15.4477C21.9532 15.5548 22.1366 15.4889 22.151 15.3381L22.7609 8.93449C22.7715 8.82254 22.6776 8.72858 22.5656 8.73924L16.162 9.34911C16.0112 9.36347 15.9453 9.5469 16.0524 9.65401L18.1578 11.7594L11.2599 18.6574L9.15444 16.5519C9.04733 16.4448 8.86389 16.5108 8.84953 16.6616L8.23967 23.0652C8.22901 23.1771 8.32296 23.2711 8.43492 23.2604L14.8385 22.6506C14.9893 22.6362 15.0553 22.4528 14.9482 22.3457L12.8427 20.2402Z" fill="#1A1A1A"/>
        </g>
        <g filter="url(#filter1_d_20847_244257)">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M19.7406 13.3423L12.8426 20.2402L14.9481 22.3456C15.0552 22.4527 14.9892 22.6362 14.8384 22.6505L8.43483 23.2604C8.32288 23.2711 8.22892 23.1771 8.23958 23.0652L8.84945 16.6616C8.86381 16.5108 9.04724 16.4448 9.15435 16.5519L11.2598 18.6573L18.1577 11.7594L16.0523 9.65398C15.9452 9.54687 16.0111 9.36344 16.1619 9.34908L22.5655 8.73921C22.6775 8.72855 22.7714 8.82251 22.7608 8.93446L22.1509 15.3381C22.1366 15.4888 21.9531 15.5548 21.846 15.4477L19.7406 13.3423ZM16.8914 11.7594L15.4191 10.2871C14.7765 9.64449 15.1723 8.54387 16.077 8.45771L22.4806 7.84784C23.1524 7.78387 23.7161 8.34763 23.6521 9.01935L23.0423 15.4229C22.9561 16.3277 21.8555 16.7235 21.2129 16.0808L19.7406 14.6086L14.1089 20.2402L15.5812 21.7125C16.2239 22.3551 15.8281 23.4557 14.9233 23.5419L8.51973 24.1518C7.848 24.2157 7.28424 23.652 7.34821 22.9803L7.95808 16.5767C8.04425 15.6719 9.14486 15.2761 9.78749 15.9188L11.2598 17.3911L16.8914 11.7594Z" fill="white"/>
        </g>
        <defs>
          <filter id="filter0_d_20847_244257" x="5.83828" y="7.53842" width="19.3234" height="19.3228" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.2"/>
            <feGaussianBlur stdDeviation="1.2"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_20847_244257"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_20847_244257" result="shape"/>
          </filter>
          <filter id="filter1_d_20847_244257" x="4.94375" y="6.64288" width="21.1125" height="21.1139" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.2"/>
            <feGaussianBlur stdDeviation="1.2"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_20847_244257"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_20847_244257" result="shape"/>
          </filter>
        </defs>
      </g>
    </svg>
  `;
};
// 左下角缩放控制器原SVG
export const ScaleBottomLeftCursorBySourceHandler = (angle: number = 0) => {
  return `
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g transform="rotate(${angle}, 16, 16)">
        <g filter="url(#filter0_d_22197_203047)">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M18.1573 11.7594L11.2593 18.6573L9.1539 16.5519C9.0468 16.4448 8.86336 16.5108 8.849 16.6615L8.23914 23.0651C8.22847 23.1771 8.32243 23.271 8.43439 23.2604L14.838 22.6505C14.9888 22.6362 15.0547 22.4527 14.9476 22.3456L12.8422 20.2402L19.7401 13.3423L21.8456 15.4477C21.9527 15.5548 22.1361 15.4888 22.1505 15.338L22.7603 8.93444C22.771 8.82249 22.677 8.72853 22.5651 8.73919L16.1615 9.34905C16.0107 9.36342 15.9447 9.54685 16.0518 9.65396L18.1573 11.7594Z" fill="#1A1A1A"/>
        </g>
        <g filter="url(#filter1_d_22197_203047)">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.2594 18.6574L18.1574 11.7594L16.0519 9.65399C15.9448 9.54688 16.0108 9.36345 16.1616 9.34909L22.5652 8.73922C22.6771 8.72856 22.7711 8.82252 22.7604 8.93447L22.1506 15.3381C22.1362 15.4889 21.9528 15.5548 21.8456 15.4477L19.7402 13.3423L12.8423 20.2402L14.9477 22.3456C15.0548 22.4528 14.9889 22.6362 14.8381 22.6506L8.43447 23.2604C8.32252 23.2711 8.22856 23.1771 8.23922 23.0652L8.84909 16.6616C8.86345 16.5108 9.04688 16.4448 9.15399 16.5519L11.2594 18.6574ZM14.1086 20.2402L15.5809 21.7125C16.2235 22.3551 15.8277 23.4558 14.923 23.5419L8.51937 24.1518C7.84764 24.2158 7.28388 23.652 7.34786 22.9803L7.95772 16.5767C8.04389 15.6719 9.1445 15.2761 9.78713 15.9188L11.2594 17.3911L16.8911 11.7594L15.4188 10.2871C14.7761 9.64449 15.1719 8.54388 16.0767 8.45772L22.4803 7.84785C23.152 7.78388 23.7158 8.34764 23.6518 9.01936L23.0419 15.423C22.9558 16.3277 21.8551 16.7235 21.2125 16.0809L19.7402 14.6086L14.1086 20.2402Z" fill="white"/>
        </g>
        <defs>
          <filter id="filter0_d_22197_203047" x="5.83828" y="7.53837" width="19.3234" height="19.3229" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.2"/>
            <feGaussianBlur stdDeviation="1.2"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_22197_203047"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_22197_203047" result="shape"/>
          </filter>
          <filter id="filter1_d_22197_203047" x="4.94375" y="6.64289" width="21.1125" height="21.1139" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.2"/>
            <feGaussianBlur stdDeviation="1.2"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_22197_203047"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_22197_203047" result="shape"/>
          </filter>
        </defs>
      </g>
    </svg>
  `;
};
// 右下角缩放控制器原SVG
export const ScaleBottomRightCursorBySourceHandler = (angle: number = 0) => {
  return `
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g transform="rotate(${angle}, 16, 16)">
        <g filter="url(#filter0_d_22197_203042)">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M12.8427 11.7594L19.7407 18.6573L21.8461 16.5519C21.9532 16.4448 22.1366 16.5108 22.151 16.6615L22.7609 23.0651C22.7715 23.1771 22.6776 23.271 22.5656 23.2604L16.162 22.6505C16.0112 22.6362 15.9453 22.4527 16.0524 22.3456L18.1578 20.2402L11.2599 13.3423L9.15444 15.4477C9.04733 15.5548 8.86389 15.4888 8.84953 15.338L8.23967 8.93444C8.22901 8.82249 8.32296 8.72853 8.43492 8.73919L14.8385 9.34905C14.9893 9.36342 15.0553 9.54685 14.9482 9.65396L12.8427 11.7594Z" fill="#1A1A1A"/>
        </g>
        <g filter="url(#filter1_d_22197_203042)">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M19.7406 18.6574L12.8426 11.7594L14.9481 9.65399C15.0552 9.54688 14.9892 9.36345 14.8384 9.34909L8.43483 8.73922C8.32288 8.72856 8.22892 8.82252 8.23958 8.93447L8.84945 15.3381C8.86381 15.4889 9.04724 15.5548 9.15435 15.4477L11.2598 13.3423L18.1577 20.2402L16.0523 22.3456C15.9452 22.4528 16.0111 22.6362 16.1619 22.6506L22.5655 23.2604C22.6775 23.2711 22.7714 23.1771 22.7608 23.0652L22.1509 16.6616C22.1366 16.5108 21.9531 16.4448 21.846 16.5519L19.7406 18.6574ZM16.8914 20.2402L15.4191 21.7125C14.7765 22.3551 15.1723 23.4558 16.077 23.5419L22.4806 24.1518C23.1524 24.2158 23.7161 23.652 23.6521 22.9803L23.0423 16.5767C22.9561 15.6719 21.8555 15.2761 21.2129 15.9188L19.7406 17.3911L14.1089 11.7594L15.5812 10.2871C16.2239 9.64449 15.8281 8.54388 14.9233 8.45772L8.51973 7.84785C7.848 7.78388 7.28424 8.34764 7.34821 9.01936L7.95808 15.423C8.04425 16.3277 9.14486 16.7235 9.78749 16.0809L11.2598 14.6086L16.8914 20.2402Z" fill="white"/>
        </g>
        <defs>
          <filter id="filter0_d_22197_203042" x="5.83828" y="7.53837" width="19.3234" height="19.3229" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.2"/>
            <feGaussianBlur stdDeviation="1.2"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_22197_203042"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_22197_203042" result="shape"/>
          </filter>
          <filter id="filter1_d_22197_203042" x="4.94375" y="6.64289" width="21.1125" height="21.1139" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.2"/>
            <feGaussianBlur stdDeviation="1.2"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_22197_203042"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_22197_203042" result="shape"/>
          </filter>
        </defs>
      </g>
    </svg>
  `;
};
// =========================缩放控制器===============================

// =========================旋转控制器===============================
// 右上角旋转控制器原SVG
export const RotateTopRightCursorBySourceHandler = (angle: number = 0) => {
  return `
 <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g transform="rotate(${angle}, 16, 16)">
    <g filter="url(#filter0_d_14195_76715)">
    <mask id="path-1-outside-1_14195_76715" maskUnits="userSpaceOnUse" x="4.89039" y="5.26027" width="21.9203" height="21.9203" fill="black">
    <rect fill="white" x="4.89039" y="5.26027" width="21.9203" height="21.9203"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0014 12.535C15.6105 12.7641 19.3069 16.4605 19.536 21.0696H16.1926C16.1035 21.0696 16.0589 21.1773 16.1219 21.2403L20.3706 25.489C20.4682 25.5866 20.6265 25.5866 20.7241 25.489L24.9728 21.2403C25.0358 21.1773 24.9912 21.0696 24.9021 21.0696L21.538 21.0696C21.306 15.3557 16.7153 10.765 11.0014 10.5329L11.0014 7.16885C11.0014 7.07976 10.8937 7.03514 10.8307 7.09814L6.58199 11.3468C6.48436 11.4445 6.48436 11.6028 6.58199 11.7004L10.8307 15.9491C10.8937 16.0121 11.0014 15.9675 11.0014 15.8784L11.0014 12.535Z"/>
    </mask>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0014 12.535C15.6105 12.7641 19.3069 16.4605 19.536 21.0696H16.1926C16.1035 21.0696 16.0589 21.1773 16.1219 21.2403L20.3706 25.489C20.4682 25.5866 20.6265 25.5866 20.7241 25.489L24.9728 21.2403C25.0358 21.1773 24.9912 21.0696 24.9021 21.0696L21.538 21.0696C21.306 15.3557 16.7153 10.765 11.0014 10.5329L11.0014 7.16885C11.0014 7.07976 10.8937 7.03514 10.8307 7.09814L6.58199 11.3468C6.48436 11.4445 6.48436 11.6028 6.58199 11.7004L10.8307 15.9491C10.8937 16.0121 11.0014 15.9675 11.0014 15.8784L11.0014 12.535Z" fill="#1A1A1A"/>
    <path d="M19.536 21.0696L20.5347 21.0199L20.5869 22.0696L19.536 22.0696L19.536 21.0696ZM11.0014 12.535L10.0014 12.535L10.0014 11.4841L11.051 11.5362L11.0014 12.535ZM24.9021 21.0696L24.9021 20.0696L24.9021 20.0696L24.9021 21.0696ZM21.538 21.0696V22.0696H20.5778L20.5389 21.1101L21.538 21.0696ZM11.0014 10.5329L10.9608 11.5321L10.0014 11.4931L10.0014 10.5329L11.0014 10.5329ZM11.0014 7.16885L12.0014 7.16884L12.0014 7.16885L11.0014 7.16885ZM11.0014 15.8784L10.0014 15.8784V15.8784H11.0014ZM18.5372 21.1192C18.3336 17.0234 15.0476 13.7373 10.9518 13.5338L11.051 11.5362C16.1734 11.7908 20.2802 15.8976 20.5347 21.0199L18.5372 21.1192ZM16.1926 20.0696H19.536L19.536 22.0696L16.1926 22.0696L16.1926 20.0696ZM15.4148 21.9474C14.7218 21.2544 15.2126 20.0696 16.1926 20.0696L16.1926 22.0696C16.9944 22.0696 17.3959 21.1001 16.829 20.5332L15.4148 21.9474ZM19.6635 26.1961L15.4148 21.9474L16.829 20.5332L21.0777 24.7819L19.6635 26.1961ZM21.4312 26.1961C20.9431 26.6842 20.1516 26.6842 19.6635 26.1961L21.0777 24.7819C20.7848 24.489 20.3099 24.489 20.017 24.7819L21.4312 26.1961ZM25.6799 21.9474L21.4312 26.1961L20.017 24.7819L24.2657 20.5332L25.6799 21.9474ZM24.9021 20.0696C25.8821 20.0696 26.3729 21.2544 25.6799 21.9474L24.2657 20.5332C23.6987 21.1001 24.1003 22.0696 24.9021 22.0696L24.9021 20.0696ZM21.538 20.0696L24.9021 20.0696L24.9021 22.0696L21.538 22.0696V20.0696ZM11.042 9.53376C17.2763 9.78694 22.284 14.7947 22.5372 21.029L20.5389 21.1101C20.3279 15.9167 16.1543 11.743 10.9608 11.5321L11.042 9.53376ZM12.0014 7.16885L12.0014 10.5329L10.0014 10.5329L10.0014 7.16885L12.0014 7.16885ZM10.1236 6.39103C10.8166 5.69805 12.0014 6.18887 12.0014 7.16884L10.0014 7.16886C10.0014 7.97064 10.9708 8.37223 11.5378 7.80524L10.1236 6.39103ZM5.87488 10.6397L10.1236 6.39103L11.5378 7.80524L7.2891 12.054L5.87488 10.6397ZM5.87488 12.4075C5.38673 11.9193 5.38673 11.1279 5.87488 10.6397L7.2891 12.054C7.58199 11.7611 7.58199 11.2862 7.2891 10.9933L5.87488 12.4075ZM10.1236 16.6562L5.87488 12.4075L7.2891 10.9933L11.5378 15.242L10.1236 16.6562ZM12.0014 15.8784C12.0014 16.8584 10.8166 17.3492 10.1236 16.6562L11.5378 15.242C10.9708 14.675 10.0014 15.0766 10.0014 15.8784L12.0014 15.8784ZM12.0014 12.535L12.0014 15.8784L10.0014 15.8784L10.0014 12.535L12.0014 12.535Z" fill="white" mask="url(#path-1-outside-1_14195_76715)"/>
    </g>
    <defs>
    <filter id="filter0_d_14195_76715" x="3.50781" y="5.06665" width="24.4961" height="24.4955" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="1"/>
    <feGaussianBlur stdDeviation="1"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_14195_76715"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_14195_76715" result="shape"/>
    </filter>
    </defs>
    </g>
  </svg> 
  `;
};
// 左上角旋转控制器原SVG
export const RotateTopLeftCursorBySourceHandler = (angle: number) => {
  return `
 <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g transform="rotate(${angle}, 16, 16)">
      <g filter="url(#filter0_d_13873_63067)">
        <mask id="path-1-outside-1_13873_63067" maskUnits="userSpaceOnUse" x="4.73618" y="4.80773" width="21.9203" height="21.9203" fill="black">
          <rect fill="white" x="4.73618" y="4.80773" width="21.9203" height="21.9203"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M20.5455 12.0824C15.9364 12.3115 12.24 16.0079 12.0109 20.617H15.3543C15.4434 20.617 15.488 20.7247 15.425 20.7877L11.1763 25.0364C11.0787 25.1341 10.9204 25.1341 10.8227 25.0364L6.57404 20.7877C6.51105 20.7247 6.55566 20.617 6.64475 20.617L10.0088 20.617C10.2409 14.9031 14.8316 10.3124 20.5455 10.0804L20.5455 6.7163C20.5455 6.62721 20.6532 6.5826 20.7162 6.64559L24.9649 10.8943C25.0625 10.9919 25.0625 11.1502 24.9649 11.2479L20.7162 15.4966C20.6532 15.5596 20.5455 15.5149 20.5455 15.4258L20.5455 12.0824Z"/>
        </mask>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M20.5455 12.0824C15.9364 12.3115 12.24 16.0079 12.0109 20.617H15.3543C15.4434 20.617 15.488 20.7247 15.425 20.7877L11.1763 25.0364C11.0787 25.1341 10.9204 25.1341 10.8227 25.0364L6.57404 20.7877C6.51105 20.7247 6.55566 20.617 6.64475 20.617L10.0088 20.617C10.2409 14.9031 14.8316 10.3124 20.5455 10.0804L20.5455 6.7163C20.5455 6.62721 20.6532 6.5826 20.7162 6.64559L24.9649 10.8943C25.0625 10.9919 25.0625 11.1502 24.9649 11.2479L20.7162 15.4966C20.6532 15.5596 20.5455 15.5149 20.5455 15.4258L20.5455 12.0824Z" fill="#1A1A1A"/>
        <path d="M12.0109 20.617L11.0121 20.5674L10.96 21.617L12.0109 21.617L12.0109 20.617ZM20.5455 12.0824L21.5455 12.0824L21.5455 11.0315L20.4958 11.0837L20.5455 12.0824ZM6.64475 20.617L6.64475 19.617L6.64475 19.617L6.64475 20.617ZM10.0088 20.617V21.617H10.9691L11.008 20.6576L10.0088 20.617ZM20.5455 10.0804L20.586 11.0796L21.5455 11.0406L21.5455 10.0804L20.5455 10.0804ZM20.5455 6.7163L19.5455 6.71629L19.5455 6.7163L20.5455 6.7163ZM20.5455 15.4258L21.5455 15.4259V15.4258H20.5455ZM13.0097 20.6667C13.2132 16.5708 16.4993 13.2848 20.5951 13.0812L20.4958 11.0837C15.3735 11.3383 11.2667 15.445 11.0121 20.5674L13.0097 20.6667ZM15.3543 19.617L12.0109 19.617L12.0109 21.617L15.3543 21.617V19.617ZM16.1321 21.4948C16.8251 20.8019 16.3343 19.617 15.3543 19.617V21.617C14.5525 21.617 14.1509 20.6476 14.7179 20.0806L16.1321 21.4948ZM11.8834 25.7435L16.1321 21.4948L14.7179 20.0806L10.4692 24.3293L11.8834 25.7435ZM10.1156 25.7435C10.6038 26.2317 11.3953 26.2317 11.8834 25.7435L10.4692 24.3293C10.7621 24.0364 11.237 24.0364 11.5299 24.3293L10.1156 25.7435ZM5.86694 21.4948L10.1156 25.7435L11.5299 24.3293L7.28115 20.0806L5.86694 21.4948ZM6.64475 19.617C5.66477 19.617 5.17397 20.8019 5.86694 21.4948L7.28115 20.0806C7.84813 20.6476 7.44656 21.617 6.64475 21.617L6.64475 19.617ZM10.0088 19.617L6.64475 19.617L6.64475 21.617L10.0088 21.617V19.617ZM20.5049 9.08121C14.2706 9.3344 9.26285 14.3421 9.00966 20.5764L11.008 20.6576C11.2189 15.4641 15.3926 11.2905 20.586 11.0796L20.5049 9.08121ZM19.5455 6.7163L19.5455 10.0804L21.5455 10.0804L21.5455 6.7163L19.5455 6.7163ZM21.4233 5.93848C20.7303 5.24551 19.5455 5.73633 19.5455 6.71629L21.5455 6.71631C21.5455 7.51809 20.5761 7.91968 20.0091 7.3527L21.4233 5.93848ZM25.672 10.1872L21.4233 5.93848L20.0091 7.3527L24.2578 11.6014L25.672 10.1872ZM25.672 11.955C26.1601 11.4668 26.1601 10.6753 25.672 10.1872L24.2578 11.6014C23.9649 11.3085 23.9649 10.8336 24.2578 10.5407L25.672 11.955ZM21.4233 16.2037L25.672 11.955L24.2578 10.5407L20.0091 14.7895L21.4233 16.2037ZM19.5455 15.4258C19.5455 16.4058 20.7303 16.8966 21.4233 16.2037L20.0091 14.7895C20.576 14.2225 21.5455 14.624 21.5455 15.4259L19.5455 15.4258ZM19.5455 12.0824L19.5455 15.4258H21.5455L21.5455 12.0824L19.5455 12.0824Z" fill="white" mask="url(#path-1-outside-1_13873_63067)"/>
      </g>
      <defs>
        <filter id="filter0_d_13873_63067" x="3.54297" y="4.61411" width="24.4961" height="24.4955" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
          <feOffset dy="1"/>
          <feGaussianBlur stdDeviation="1"/>
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_13873_63067"/>
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_13873_63067" result="shape"/>
        </filter>
      </defs>
    </g>
  </svg>
  `;
};
// 左下角旋转控制器原SVG
export const RotateBottomLeftCursorBySourceHandler = (angle: number = 0) => {
  return `
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g transform="rotate(${angle}, 16, 16)">
        <g filter="url(#filter0_d_22197_203064)">
          <mask id="path-1-outside-1_22197_203064" maskUnits="userSpaceOnUse" x="4.73662" y="4.7071" width="21.9203" height="21.9203" fill="black">
            <rect fill="white" x="4.73662" y="4.7071" width="21.9203" height="21.9203"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M12.0113 10.8181C12.2404 15.4272 15.9368 19.1236 20.5459 19.3527L20.5459 16.0093C20.5459 15.9202 20.6536 15.8756 20.7166 15.9386L24.9653 20.1873C25.063 20.2849 25.063 20.4432 24.9653 20.5408L20.7166 24.7895C20.6536 24.8525 20.5459 24.8079 20.5459 24.7188L20.5459 21.3547C14.832 21.1227 10.2413 16.532 10.0093 10.8181L6.6452 10.8181C6.5561 10.8181 6.51149 10.7104 6.57448 10.6474L10.8232 6.3987C10.9208 6.30107 11.0791 6.30107 11.1767 6.3987L15.4255 10.6474C15.4884 10.7104 15.4438 10.8181 15.3547 10.8181L12.0113 10.8181Z"/>
          </mask>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M12.0113 10.8181C12.2404 15.4272 15.9368 19.1236 20.5459 19.3527L20.5459 16.0093C20.5459 15.9202 20.6536 15.8756 20.7166 15.9386L24.9653 20.1873C25.063 20.2849 25.063 20.4432 24.9653 20.5408L20.7166 24.7895C20.6536 24.8525 20.5459 24.8079 20.5459 24.7188L20.5459 21.3547C14.832 21.1227 10.2413 16.532 10.0093 10.8181L6.6452 10.8181C6.5561 10.8181 6.51149 10.7104 6.57448 10.6474L10.8232 6.3987C10.9208 6.30107 11.0791 6.30107 11.1767 6.3987L15.4255 10.6474C15.4884 10.7104 15.4438 10.8181 15.3547 10.8181L12.0113 10.8181Z" fill="#1A1A1A"/>
          <path d="M20.5459 19.3527L20.4963 20.3515L21.5459 20.4036L21.5459 19.3527L20.5459 19.3527ZM12.0113 10.8181L12.0113 9.81812L10.9604 9.81812L11.0126 10.8678L12.0113 10.8181ZM20.5459 24.7188L19.5459 24.7188L19.5459 24.7188L20.5459 24.7188ZM20.5459 21.3547L21.5459 21.3547L21.5459 20.3945L20.5865 20.3556L20.5459 21.3547ZM10.0093 10.8181L11.0085 10.7775L10.9695 9.81812L10.0093 9.81812L10.0093 10.8181ZM6.6452 10.8181L6.64519 11.8181L6.6452 11.8181L6.6452 10.8181ZM15.3547 10.8181L15.3547 9.81812L15.3547 9.81812L15.3547 10.8181ZM20.5955 18.3539C16.4997 18.1504 13.2137 14.8643 13.0101 10.7685L11.0126 10.8678C11.2672 15.9901 15.3739 20.0969 20.4963 20.3515L20.5955 18.3539ZM19.5459 16.0093L19.5459 19.3527L21.5459 19.3527L21.5459 16.0093L19.5459 16.0093ZM21.4237 15.2315C20.7308 14.5385 19.5459 15.0293 19.5459 16.0093L21.5459 16.0093C21.5459 16.8111 20.5765 17.2127 20.0095 16.6457L21.4237 15.2315ZM25.6724 19.4802L21.4237 15.2315L20.0095 16.6457L24.2582 20.8944L25.6724 19.4802ZM25.6724 21.2479C26.1606 20.7598 26.1606 19.9683 25.6724 19.4802L24.2582 20.8944C23.9653 20.6015 23.9653 20.1266 24.2582 19.8337L25.6724 21.2479ZM21.4237 25.4967L25.6724 21.2479L24.2582 19.8337L20.0095 24.0824L21.4237 25.4967ZM19.5459 24.7188C19.5459 25.6988 20.7308 26.1896 21.4237 25.4967L20.0095 24.0824C20.5765 23.5155 21.5459 23.917 21.5459 24.7188L19.5459 24.7188ZM19.5459 21.3547L19.5459 24.7188L21.5459 24.7188L21.5459 21.3547L19.5459 21.3547ZM9.0101 10.8587C9.26329 17.093 14.271 22.1007 20.5053 22.3539L20.5865 20.3556C15.393 20.1447 11.2194 15.971 11.0085 10.7775L9.0101 10.8587ZM6.6452 11.8181L10.0093 11.8181L10.0093 9.81812L6.6452 9.81812L6.6452 11.8181ZM5.86738 9.9403C5.1744 10.6333 5.66522 11.8181 6.64519 11.8181L6.64521 9.81812C7.44699 9.81813 7.84858 10.7875 7.28159 11.3545L5.86738 9.9403ZM10.1161 5.6916L5.86738 9.9403L7.28159 11.3545L11.5303 7.10581L10.1161 5.6916ZM11.8839 5.6916C11.3957 5.20344 10.6042 5.20344 10.1161 5.6916L11.5303 7.10581C11.2374 7.3987 10.7625 7.3987 10.4696 7.10581L11.8839 5.6916ZM16.1326 9.9403L11.8839 5.6916L10.4696 7.10581L14.7183 11.3545L16.1326 9.9403ZM15.3547 11.8181C16.3347 11.8181 16.8255 10.6333 16.1326 9.9403L14.7183 11.3545C14.1514 10.7875 14.5529 9.81812 15.3547 9.81812L15.3547 11.8181ZM12.0113 11.8181L15.3547 11.8181L15.3547 9.81812L12.0113 9.81812L12.0113 11.8181Z" fill="white" mask="url(#path-1-outside-1_22197_203064)"/>
        </g>
        <defs>
          <filter id="filter0_d_22197_203064" x="3.54297" y="4.32547" width="24.4961" height="24.4956" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1"/>
            <feGaussianBlur stdDeviation="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_22197_203064"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_22197_203064" result="shape"/>
          </filter>
        </defs>
      </g>
    </svg>
  `;
};
// 右下角旋转控制器原SVG
export const RotateBottomRightCursorBySourceHandler = (angle: number = 0) => {
  return `
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g transform="rotate(${angle}, 16, 16)">
        <g filter="url(#filter0_d_22197_203070)">
          <mask id="path-1-outside-1_22197_203070" maskUnits="userSpaceOnUse" x="4.88995" y="5.15965" width="21.9203" height="21.9203" fill="black">
            <rect fill="white" x="4.88995" y="5.15965" width="21.9203" height="21.9203"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M19.5355 11.2707C19.3065 15.8798 15.6101 19.5762 11.001 19.8052L11.001 16.4618C11.001 16.3727 10.8933 16.3281 10.8303 16.3911L6.58155 20.6398C6.48392 20.7375 6.48392 20.8958 6.58155 20.9934L10.8303 25.2421C10.8933 25.3051 11.001 25.2605 11.001 25.1714L11.001 21.8073C16.7149 21.5752 21.3055 16.9846 21.5376 11.2707L24.9017 11.2707C24.9908 11.2707 25.0354 11.163 24.9724 11.1L20.7237 6.85125C20.6261 6.75362 20.4678 6.75362 20.3701 6.85125L16.1214 11.1C16.0584 11.163 16.103 11.2707 16.1921 11.2707L19.5355 11.2707Z"/>
          </mask>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M19.5355 11.2707C19.3065 15.8798 15.6101 19.5762 11.001 19.8052L11.001 16.4618C11.001 16.3727 10.8933 16.3281 10.8303 16.3911L6.58155 20.6398C6.48392 20.7375 6.48392 20.8958 6.58155 20.9934L10.8303 25.2421C10.8933 25.3051 11.001 25.2605 11.001 25.1714L11.001 21.8073C16.7149 21.5752 21.3055 16.9846 21.5376 11.2707L24.9017 11.2707C24.9908 11.2707 25.0354 11.163 24.9724 11.1L20.7237 6.85125C20.6261 6.75362 20.4678 6.75362 20.3701 6.85125L16.1214 11.1C16.0584 11.163 16.103 11.2707 16.1921 11.2707L19.5355 11.2707Z" fill="#1A1A1A"/>
          <path d="M11.001 19.8052L11.0506 20.804L10.001 20.8562L10.001 19.8052L11.001 19.8052ZM19.5355 11.2707L19.5355 10.2707L20.5865 10.2707L20.5343 11.3203L19.5355 11.2707ZM11.001 25.1714L12.001 25.1714L12.001 25.1714L11.001 25.1714ZM11.001 21.8073L10.001 21.8073L10.001 20.8471L10.9604 20.8081L11.001 21.8073ZM21.5376 11.2707L20.5384 11.2301L20.5774 10.2707L21.5376 10.2707L21.5376 11.2707ZM24.9017 11.2707L24.9017 12.2707L24.9017 12.2707L24.9017 11.2707ZM20.3701 6.85125L19.663 6.14414L20.3701 6.85125ZM16.1214 11.1L16.8285 11.8071L16.1214 11.1ZM16.1921 11.2707L16.1921 10.2707L16.1921 10.2707L16.1921 11.2707ZM10.9513 18.8065C15.0472 18.6029 18.3332 15.3169 18.5368 11.221L20.5343 11.3203C20.2797 16.4427 16.173 20.5494 11.0506 20.804L10.9513 18.8065ZM12.001 16.4618L12.001 19.8052L10.001 19.8052L10.001 16.4618L12.001 16.4618ZM10.1231 15.684C10.8161 14.9911 12.001 15.4818 12.001 16.4618L10.001 16.4618C10.001 17.2636 10.9704 17.6652 11.5374 17.0982L10.1231 15.684ZM5.87444 19.9327L10.1231 15.684L11.5374 17.0982L7.28865 21.3469L5.87444 19.9327ZM5.87444 21.7005C5.38629 21.2123 5.38629 20.4209 5.87444 19.9327L7.28865 21.3469C7.58155 21.054 7.58155 20.5792 7.28865 20.2863L5.87444 21.7005ZM10.1231 25.9492L5.87444 21.7005L7.28865 20.2863L11.5374 24.535L10.1231 25.9492ZM12.001 25.1714C12.001 26.1514 10.8161 26.6422 10.1231 25.9492L11.5374 24.535C10.9704 23.968 10.001 24.3696 10.001 25.1714L12.001 25.1714ZM12.001 21.8073L12.001 25.1714L10.001 25.1714L10.001 21.8073L12.001 21.8073ZM22.5368 11.3112C22.2836 17.5456 17.2759 22.5533 11.0415 22.8065L10.9604 20.8081C16.1539 20.5972 20.3275 16.4236 20.5384 11.2301L22.5368 11.3112ZM24.9017 12.2707L21.5376 12.2707L21.5376 10.2707L24.9017 10.2707L24.9017 12.2707ZM25.6795 10.3928C26.3725 11.0858 25.8817 12.2707 24.9017 12.2707L24.9017 10.2707C24.0999 10.2707 23.6983 11.2401 24.2653 11.8071L25.6795 10.3928ZM21.4308 6.14414L25.6795 10.3928L24.2653 11.8071L20.0166 7.55835L21.4308 6.14414ZM19.663 6.14414C20.1512 5.65599 20.9426 5.65599 21.4308 6.14414L20.0166 7.55835C20.3095 7.85125 20.7843 7.85125 21.0772 7.55835L19.663 6.14414ZM15.4143 10.3928L19.663 6.14414L21.0772 7.55835L16.8285 11.8071L15.4143 10.3928ZM16.1921 12.2707C15.2121 12.2707 14.7214 11.0858 15.4143 10.3928L16.8285 11.8071C17.3955 11.2401 16.9939 10.2707 16.1921 10.2707L16.1921 12.2707ZM19.5355 12.2707L16.1921 12.2707L16.1921 10.2707L19.5355 10.2707L19.5355 12.2707Z" fill="white" mask="url(#path-1-outside-1_22197_203070)"/>
        </g>
        <defs>
          <filter id="filter0_d_22197_203070" x="3.50781" y="4.77802" width="24.4961" height="24.4956" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1"/>
            <feGaussianBlur stdDeviation="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_22197_203070"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_22197_203070" result="shape"/>
          </filter>
        </defs>
      </g>
    </svg>
  `;
};
// ==================================旋转控制器======================================

// 左上角旋转控制器鼠标样式
export const RotateTopLeftCursor = `url("data:image/svg+xml;base64,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") 16 16, auto`;
// 左下角旋转控制器鼠标样式
export const RotateBottomLeftCursor = `url("data:image/svg+xml;base64,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") 16 16,auto`;
// 右下角旋转控制器鼠标样式
export const RotateBottomRightCursor = `url("data:image/svg+xml;base64,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") 16 16,auto`;
// 移动画布的默认鼠标状态
export const MoveCanvasOfDefault = `url("data:image/svg+xml,%3Csvg%20width%3D%2232%22%20height%3D%2232%22%20viewBox%3D%220%200%2032%2032%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20filter%3D%22url(%23filter0_d_22285_271114)%22%3E%3Cpath%20d%3D%22M11.99%2016.6758C11.8732%2016.2088%2011.7565%2015.7419%2011.523%2014.8079C11.2895%2014.1074%2011.1727%2013.7572%2010.9393%2013.4069C10.7058%2012.8232%2010.589%2012.5897%2010.3555%2012.006C10.2388%2011.6557%209.88854%2010.8385%209.77179%2010.3715C9.65505%209.78779%209.77179%209.32081%2010.0053%208.97057C10.3555%208.62033%2011.1727%208.38684%2011.6397%208.50358C12.1067%208.62033%2012.457%209.08732%2012.6904%209.43755C13.0407%2010.0213%2013.1574%2010.138%2013.5077%2011.1887C13.9747%2012.3562%2014.2082%2013.4069%2014.2082%2013.7572L14.3249%2014.3409C14.3249%2014.3409%2014.3249%2013.0567%2014.3249%2012.9399C14.3249%2011.7725%2014.2082%2010.8385%2014.3249%209.5543C14.3249%209.43755%2014.4416%208.85382%2014.4416%208.73708C14.5584%208.15335%2014.7919%207.80311%2015.2589%207.56961C15.7259%207.33612%2016.3096%207.33612%2016.8933%207.56961C17.3603%207.80311%2017.5938%208.15335%2017.7105%208.73708C17.7105%208.85382%2017.8273%209.90454%2017.8273%2010.0213C17.8273%2011.1887%2017.8273%2011.8892%2017.8273%2012.5897C17.8273%2012.8232%2017.8273%2014.4576%2017.8273%2014.3409C17.944%2013.5237%2017.944%2010.605%2018.1775%209.78779C18.2943%209.32081%2018.6445%208.97057%2019.1115%208.73708C19.5785%208.50358%2020.3957%208.62033%2020.7459%208.97057C21.0962%209.32081%2021.2129%209.78779%2021.3297%2010.3715C21.3297%2010.8385%2021.3297%2011.4222%2021.3297%2011.7725C21.3297%2012.8232%2021.3297%2013.2902%2021.3297%2014.2242C21.3297%2014.2242%2021.3297%2014.5744%2021.3297%2014.4576C21.4464%2014.1074%2021.5632%2013.8739%2021.6799%2013.6404C21.6799%2013.5237%2021.9134%2012.9399%2022.1469%2012.5897C22.2636%2012.3562%2022.3804%2012.1227%2022.6139%2011.7725C22.8474%2011.4222%2023.0809%2011.3055%2023.4311%2011.072C24.0148%2010.8385%2024.7153%2011.1887%2024.9488%2011.7725C25.0656%2012.006%2024.9488%2012.5897%2024.9488%2013.0567C24.8321%2013.7572%2024.5986%2014.5744%2024.4818%2014.9246C24.3651%2015.3916%2024.1316%2016.3256%2024.1316%2016.7926C24.0148%2017.2596%2023.8981%2018.427%2023.6646%2018.894C23.5479%2019.2442%2023.1976%2020.0615%2022.8474%2020.5285C22.8474%2020.5285%2021.5632%2021.9294%2021.4464%2022.6299C21.3297%2023.3304%2021.3297%2023.3304%2021.3297%2023.7973C21.3297%2024.2643%2021.4464%2024.8481%2021.4464%2024.8481C21.4464%2024.8481%2020.5125%2024.9648%2020.0455%2024.8481C19.5785%2024.7313%2018.9947%2023.9141%2018.878%2023.5639C18.6445%2023.2136%2018.2943%2023.2136%2018.0608%2023.5639C17.8273%2024.0308%2017.2436%2024.8481%2016.7766%2024.8481C15.9593%2024.9648%2014.3249%2024.8481%2013.1574%2024.8481C13.1574%2024.8481%2013.3909%2023.6806%2012.9239%2023.2136C12.5737%2022.8634%2011.99%2022.2796%2011.6397%2021.9294L10.7058%2020.8787C10.3555%2020.4117%2010.0053%2019.5945%209.30481%2018.5438C8.95457%2017.96%208.13734%2017.2596%207.78711%2016.6758C7.55361%2016.2088%207.43687%2015.5084%207.55361%2015.1581C7.78711%2014.4576%208.37084%2014.1074%209.18806%2014.2242C9.77179%2014.2242%2010.122%2014.4576%2010.589%2014.8079C10.8225%2015.0414%2011.2895%2015.3916%2011.523%2015.6251C11.7565%2015.8586%2011.7565%2015.9753%2011.99%2016.2088C11.99%2016.9093%2011.99%2017.0261%2011.99%2016.6758Z%22%20fill%3D%22white%22%2F%3E%3Cpath%20d%3D%22M11.99%2016.6758C11.8732%2016.2088%2011.7565%2015.7419%2011.523%2014.8079C11.2895%2014.1074%2011.1727%2013.7572%2010.9393%2013.4069C10.7058%2012.8232%2010.589%2012.5897%2010.3555%2012.006C10.2388%2011.6557%209.88854%2010.8385%209.77179%2010.3715C9.65505%209.78779%209.77179%209.32081%2010.0053%208.97057C10.3555%208.62033%2011.1727%208.38684%2011.6397%208.50358C12.1067%208.62033%2012.457%209.08732%2012.6904%209.43755C13.0407%2010.0213%2013.1574%2010.138%2013.5077%2011.1887C13.9747%2012.3562%2014.2082%2013.4069%2014.2082%2013.7572L14.3249%2014.3409C14.3249%2014.3409%2014.3249%2013.0567%2014.3249%2012.9399C14.3249%2011.7725%2014.2082%2010.8385%2014.3249%209.5543C14.3249%209.43755%2014.4416%208.85382%2014.4416%208.73708C14.5584%208.15335%2014.7919%207.80311%2015.2589%207.56961C15.7259%207.33612%2016.3096%207.33612%2016.8933%207.56961C17.3603%207.80311%2017.5938%208.15335%2017.7105%208.73708C17.7105%208.85382%2017.8273%209.90454%2017.8273%2010.0213C17.8273%2011.1887%2017.8273%2011.8892%2017.8273%2012.5897C17.8273%2012.8232%2017.8273%2014.4576%2017.8273%2014.3409C17.944%2013.5237%2017.944%2010.605%2018.1775%209.78779C18.2943%209.32081%2018.6445%208.97057%2019.1115%208.73708C19.5785%208.50358%2020.3957%208.62033%2020.7459%208.97057C21.0962%209.32081%2021.2129%209.78779%2021.3297%2010.3715C21.3297%2010.8385%2021.3297%2011.4222%2021.3297%2011.7725C21.3297%2012.8232%2021.3297%2013.2902%2021.3297%2014.2242C21.3297%2014.2242%2021.3297%2014.5744%2021.3297%2014.4576C21.4464%2014.1074%2021.5632%2013.8739%2021.6799%2013.6404C21.6799%2013.5237%2021.9134%2012.9399%2022.1469%2012.5897C22.2636%2012.3562%2022.3804%2012.1227%2022.6139%2011.7725C22.8474%2011.4222%2023.0809%2011.3055%2023.4311%2011.072C24.0148%2010.8385%2024.7153%2011.1887%2024.9488%2011.7725C25.0656%2012.006%2024.9488%2012.5897%2024.9488%2013.0567C24.8321%2013.7572%2024.5986%2014.5744%2024.4818%2014.9246C24.3651%2015.3916%2024.1316%2016.3256%2024.1316%2016.7926C24.0148%2017.2596%2023.8981%2018.427%2023.6646%2018.894C23.5479%2019.2442%2023.1976%2020.0615%2022.8474%2020.5285C22.8474%2020.5285%2021.5632%2021.9294%2021.4464%2022.6299C21.3297%2023.3304%2021.3297%2023.3304%2021.3297%2023.7973C21.3297%2024.2643%2021.4464%2024.8481%2021.4464%2024.8481C21.4464%2024.8481%2020.5125%2024.9648%2020.0455%2024.8481C19.5785%2024.7313%2018.9947%2023.9141%2018.878%2023.5639C18.6445%2023.2136%2018.2943%2023.2136%2018.0608%2023.5639C17.8273%2024.0308%2017.2436%2024.8481%2016.7766%2024.8481C15.9593%2024.9648%2014.3249%2024.8481%2013.1574%2024.8481C13.1574%2024.8481%2013.3909%2023.6806%2012.9239%2023.2136C12.5737%2022.8634%2011.99%2022.2796%2011.6397%2021.9294L10.7058%2020.8787C10.3555%2020.4117%2010.0053%2019.5945%209.30481%2018.5438C8.95457%2017.96%208.13734%2017.2596%207.78711%2016.6758C7.55361%2016.2088%207.43687%2015.5084%207.55361%2015.1581C7.78711%2014.4576%208.37084%2014.1074%209.18806%2014.2242C9.77179%2014.2242%2010.122%2014.4576%2010.589%2014.8079C10.8225%2015.0414%2011.2895%2015.3916%2011.523%2015.6251C11.7565%2015.8586%2011.7565%2015.9753%2011.99%2016.2088C11.99%2016.9093%2011.99%2017.0261%2011.99%2016.6758Z%22%20stroke%3D%22black%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M16.6837%2015.8258C16.9597%2015.8172%2017.1904%2016.034%2017.199%2016.31L17.3226%2020.2794C17.3311%2020.5554%2017.1144%2020.7861%2016.8383%2020.7947C16.5623%2020.8033%2016.3316%2020.5865%2016.323%2020.3105L16.1995%2016.3411C16.1909%2016.0651%2016.4077%2015.8344%2016.6837%2015.8258ZM19.2973%2015.8256C19.5734%2015.8256%2019.7973%2016.0494%2019.7973%2016.3256V20.295C19.7973%2020.5711%2019.5734%2020.795%2019.2973%2020.795C19.0211%2020.795%2018.7973%2020.5711%2018.7973%2020.295V16.3256C18.7973%2016.0494%2019.0211%2015.8256%2019.2973%2015.8256ZM14.8555%2016.3256C14.8555%2016.0494%2014.6316%2015.8256%2014.3555%2015.8256C14.0793%2015.8256%2013.8555%2016.0494%2013.8555%2016.3256V20.295C13.8555%2020.5711%2014.0793%2020.795%2014.3555%2020.795C14.6316%2020.795%2014.8555%2020.5711%2014.8555%2020.295V16.3256Z%22%20fill%3D%22black%22%2F%3E%3C%2Fg%3E%3Cdefs%3E%3Cfilter%20id%3D%22filter0_d_22285_271114%22%20x%3D%225.00781%22%20y%3D%225.89449%22%20width%3D%2222.4922%22%20height%3D%2222.5055%22%20filterUnits%3D%22userSpaceOnUse%22%20color-interpolation-filters%3D%22sRGB%22%3E%3CfeFlood%20flood-opacity%3D%220%22%20result%3D%22BackgroundImageFix%22%2F%3E%3CfeColorMatrix%20in%3D%22SourceAlpha%22%20type%3D%22matrix%22%20values%3D%220%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%20127%200%22%20result%3D%22hardAlpha%22%2F%3E%3CfeOffset%20dy%3D%221%22%2F%3E%3CfeGaussianBlur%20stdDeviation%3D%221%22%2F%3E%3CfeColorMatrix%20type%3D%22matrix%22%20values%3D%220%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200.15%200%22%2F%3E%3CfeBlend%20mode%3D%22normal%22%20in2%3D%22BackgroundImageFix%22%20result%3D%22effect1_dropShadow_22285_271114%22%2F%3E%3CfeBlend%20mode%3D%22normal%22%20in%3D%22SourceGraphic%22%20in2%3D%22effect1_dropShadow_22285_271114%22%20result%3D%22shape%22%2F%3E%3C%2Ffilter%3E%3C%2Fdefs%3E%3C%2Fsvg%3E") 16 16,auto`;
// 移动画布的鼠标按下鼠标状态
export const MovecanvasOfMouseDown = `url("data:image/svg+xml,%3Csvg%20width%3D%2232%22%20height%3D%2232%22%20viewBox%3D%220%200%2032%2032%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20filter%3D%22url(%23filter0_d_22298_271157)%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M10.1736%2010.3954C9.20156%2010.7567%208.79275%2011.4588%208.76591%2012.2002C8.74833%2012.6885%208.85309%2013.1504%209.06821%2013.7525C9.03425%2013.6575%209.19476%2014.1355%209.22667%2014.2412C9.29275%2014.4624%208.59036%2014.1645%208.1624%2014.2509C7.68683%2014.3439%207.05358%2014.7522%206.7237%2015.2939C6.33602%2015.9288%206.31702%2016.6589%206.61599%2017.9727C6.77936%2018.6885%207.09429%2019.2914%207.54555%2019.8637C7.74604%2020.118%208.29279%2020.7102%208.33426%2020.763L9.444%2021.9931C9.64122%2022.1708%209.78135%2022.302%2010.1972%2022.6968C10.6088%2023.0875%2010.8049%2023.2696%2010.9859%2023.4239C11.0624%2023.4883%2011.127%2023.6589%2011.1525%2023.9125C11.1736%2024.1212%2011.1663%2024.3539%2011.1407%2024.5782C11.1319%2024.6558%2011.1234%2024.7117%2011.1185%2024.7384C11.0637%2025.0378%2011.2921%2025.3143%2011.5964%2025.3171C11.8787%2025.3197%2012.1003%2025.3232%2012.6717%2025.3332C12.7957%2025.3354%2012.7957%2025.3354%2012.9201%2025.3376C14.4486%2025.3638%2015.2128%2025.3569%2015.7686%2025.2871C16.3627%2025.2122%2016.9803%2024.488%2017.5041%2023.5925C17.9525%2024.3914%2018.5746%2025.1695%2019.2037%2025.2692C19.4012%2025.3011%2019.6422%2025.3114%2019.9214%2025.3053C20.1315%2025.3007%2020.3539%2025.2869%2020.5782%2025.2669C20.7452%2025.252%2020.8781%2025.237%2020.9575%2025.2267C21.2529%2025.1883%2021.4462%2024.8976%2021.3672%2024.6104C21.3502%2024.5484%2021.3228%2024.4345%2021.2951%2024.2911C21.263%2024.1241%2021.2398%2023.961%2021.2293%2023.8124C21.2227%2023.7192%2021.2215%2023.6347%2021.2257%2023.5615C21.2334%2023.4332%2021.2359%2023.3614%2021.2398%2023.1938C21.2412%2023.1351%2021.2412%2023.1351%2021.243%2023.0833C21.2501%2022.9048%2021.2736%2022.7368%2021.3496%2022.371C21.3824%2022.2139%2021.5423%2021.9104%2021.7942%2021.5347C21.8689%2021.4232%2021.9505%2021.3069%2022.038%2021.1867C22.2044%2020.9582%2022.3835%2020.7272%2022.5626%2020.5062C22.6699%2020.3738%2022.7529%2020.2748%2022.7991%2020.221C23.1872%2019.6668%2023.6005%2018.7586%2023.7527%2018.2261C23.8988%2017.7141%2024.0173%2016.7548%2024.0853%2015.8499C24.1245%2015.3272%2024.139%2014.9033%2024.139%2014.2412C24.1391%2014.1135%2024.1391%2014.1135%2024.1394%2014.0101C24.1399%2013.8647%2024.1399%2013.8647%2024.1396%2013.7392C24.1384%2013.5341%2024.1323%2013.3295%2024.1125%2012.8302C24.0715%2011.767%2023.4884%2011.0788%2022.6288%2010.9461C21.8976%2010.8333%2021.2443%2011.0532%2020.9575%2011.3597C20.8587%2011.4653%2020.9623%2010.9959%2020.675%2010.5448C20.46%2010.2068%2019.8993%209.77438%2019.4706%209.69003C19.0319%209.60456%2018.5089%209.61402%2018.0298%209.69876C17.6101%209.77387%2017.1325%2010.14%2016.8897%2010.5448C16.7274%2010.8153%2016.9405%2010.537%2016.7671%2010.2387C16.5287%209.82776%2015.9726%209.46074%2015.4409%209.35202C14.9878%209.25865%2014.4707%209.29378%2014.003%209.43492C13.4362%209.60377%2012.6717%2010.2387%2012.7619%2010.9461C12.3123%2010.2387%2011.1267%2010.0419%2010.1736%2010.3954Z%22%20fill%3D%22black%22%2F%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M12.5046%2012.8432C12.5414%2012.9554%2012.339%2012.3231%2012.2762%2012.143C12.2112%2011.9565%2012.1509%2011.8018%2012.0944%2011.6792C11.9271%2011.3165%2011.0424%2011.1202%2010.5174%2011.3149C9.95828%2011.5228%209.76214%2011.8596%209.74853%2012.2356C9.73648%2012.5703%209.81763%2012.928%209.99429%2013.4226C9.94775%2013.2923%2010.1833%2013.9341%2010.2253%2014.0734C10.2707%2014.2254%2010.3392%2014.3809%2010.4532%2014.6025C10.7019%2015.0857%2010.7393%2015.1554%2010.7619%2015.2296C10.7707%2015.2585%2010.7773%2015.2882%2010.7933%2015.3433C10.8359%2015.4873%2010.8573%2015.5738%2010.8647%2015.6534C10.9084%2016.1222%2010.3806%2016.4154%2010.0139%2016.0798C9.95919%2016.0297%209.91827%2015.9787%209.83909%2015.8719L9.83529%2015.8667C9.75332%2015.755%209.6994%2015.6815%209.65288%2015.6194C9.58507%2015.5295%209.53526%2015.4743%209.49045%2015.4357C9.46145%2015.4107%209.43015%2015.3888%209.38343%2015.361C9.34391%2015.3375%209.29873%2015.3127%209.16922%2015.2421C9.07733%2015.1913%208.58053%2015.1676%208.35603%2015.2129C8.13656%2015.2558%207.74328%2015.5094%207.56354%2015.8046C7.34019%2016.1703%207.32726%2016.6673%207.57473%2017.7547C7.70266%2018.3153%207.95105%2018.7908%208.31816%2019.2564C8.42234%2019.3885%208.67068%2019.6649%208.85775%2019.8731C8.97384%2020.0023%209.06633%2020.1052%209.08614%2020.1311L10.1341%2021.296C10.3048%2021.4461%2010.4475%2021.5797%2010.8834%2021.9934C11.2722%2022.3626%2011.4623%2022.539%2011.6224%2022.6755C11.933%2022.9372%2012.0821%2023.331%2012.1308%2023.8141C12.1439%2023.9437%2012.0885%2024.0617%2012.0459%2024.1526C11.9938%2024.2636%2011.9607%2024.3342%2012.0944%2024.3363C12.2414%2024.3385%2012.4739%2024.3448%2012.6623%2024.3499C12.7857%2024.3532%2012.8902%2024.356%2012.9393%2024.3568C14.4108%2024.3821%2015.1594%2024.3754%2015.6485%2024.3139C15.7988%2024.295%2016.3097%2023.6959%2016.6609%2023.0954C17.0543%2022.4297%2018.0139%2022.3931%2018.4092%2023.1469C18.6678%2023.6393%2019.1728%2024.2708%2019.3611%2024.3007C19.4906%2024.3216%2019.6776%2024.3296%2019.9023%2024.3247C20.0881%2024.3206%2020.3917%2024.3363%2020.3917%2024.3363C20.3917%2024.3363%2020.2668%2024.0732%2020.2533%2023.8813C20.2439%2023.7487%2020.242%2023.6231%2020.2489%2023.5037C20.2558%2023.3894%2020.2579%2023.327%2020.2616%2023.1708L20.2616%2023.1705C20.2631%2023.1085%2020.2631%2023.1084%2020.2653%2023.0443C20.2748%2022.8055%2020.3055%2022.5861%2020.3917%2022.1712C20.458%2021.853%2020.6627%2021.4645%2020.9818%2020.9885C21.064%2020.866%2021.1528%2020.7393%2021.2474%2020.6094C21.4248%2020.3659%2021.6139%2020.1219%2021.803%2019.8886C21.9166%2019.7484%2022.0055%2019.6423%2022.0267%2019.6216C22.3193%2019.2003%2022.6881%2018.39%2022.8119%2017.9568C22.9332%2017.5315%2023.0467%2016.6131%2023.1096%2015.7766C23.1468%2015.2794%2023.1605%2014.8798%2023.1605%2014.2412C23.1606%2014.1694%2023.1606%2014.1373%2023.1607%2014.1053C23.1607%2014.0795%2023.1608%2014.0537%2023.1609%2014.0072C23.1612%2013.9274%2023.1613%2013.8916%2023.1613%2013.8558C23.1613%2013.8267%2023.1612%2013.7976%2023.1611%2013.7448C23.16%2013.5537%2023.1542%2013.3581%2023.1348%2012.8685C23.1115%2012.2641%2022.8665%2011.9749%2022.4815%2011.9155C22.123%2011.8602%2021.7141%2012.029%2021.5559%2012.2779C21.5151%2012.3418%2021.4753%2012.4224%2021.4376%2012.5169C21.3836%2012.6522%2021.3478%2012.7714%2021.2923%2012.9819C21.2695%2013.0711%2021.2531%2013.1291%2021.2322%2013.1796C21.2367%2013.2144%2021.2401%2013.2407%2021.2312%2013.2618C21.2041%2013.3263%2021.0622%2013.3425%2020.4863%2013.4083L20.4863%2013.4083C20.2584%2013.0897%2020.2584%2013.0897%2020.2718%2013.0293C20.1801%2012.1816%2020.0188%2011.3367%2019.85%2011.0714C19.7697%2010.9453%2019.4264%2010.6805%2019.2846%2010.6526C18.9748%2010.5923%2018.5707%2010.5996%2018.204%2010.6645C18.0574%2010.6907%2017.7329%2010.905%2017.6397%2011.0605C17.5719%2011.1733%2017.5069%2011.4554%2017.4573%2011.8633C17.4356%2012.0416%2017.3671%2012.7585%2017.3847%2012.5841C17.3656%2012.7738%2017.3495%2012.8939%2017.3248%2012.9987C17.3159%2013.0367%2017.3159%2013.0367%2017.2695%2013.1333C17.1207%2013.3543%2017.1207%2013.3543%2016.5808%2013.295C16.4028%2013.1799%2016.29%2012.2825%2016.2316%2011.8184C16.2162%2011.6959%2016.2046%2011.6036%2016.1966%2011.5638C16.1248%2011.2075%2016.0312%2010.9204%2015.9213%2010.7312C15.8309%2010.5755%2015.5169%2010.3682%2015.246%2010.3128C14.9602%2010.2539%2014.607%2010.2779%2014.2871%2010.3745C14.038%2010.4487%2013.8272%2010.6735%2013.7074%2010.9848C13.6432%2011.1512%2013.6031%2011.4335%2013.5859%2011.82C13.5784%2011.9876%2013.5761%2012.1151%2013.5729%2012.4212C13.5702%2012.6822%2013.5677%2012.7945%2013.5603%2012.9092C13.6084%2013.0187%2013.6084%2013.0187%2013.063%2013.4C12.6897%2013.221%2012.619%2013.1872%2012.6041%2013.1368C12.6007%2013.1251%2012.6002%2013.1125%2012.5997%2013.0969C12.5706%2013.037%2012.5445%2012.9648%2012.5046%2012.8432ZM15.7753%2021.208C15.5044%2021.2093%2015.2838%2020.9907%2015.2826%2020.7198L15.2616%2016.1778C15.2604%2015.9069%2015.479%2015.6863%2015.7498%2015.6851C16.0207%2015.6838%2016.2412%2015.9024%2016.2425%2016.1733L16.2634%2020.7153C16.2647%2020.9862%2016.0461%2021.2068%2015.7753%2021.208ZM18.4083%2021.1927C18.1375%2021.1927%2017.9179%2020.9731%2017.9179%2020.7023V16.1785C17.9179%2015.9077%2018.1375%2015.6881%2018.4083%2015.6881C18.6792%2015.6881%2018.8988%2015.9077%2018.8988%2016.1785V20.7023C18.8988%2020.9731%2018.6792%2021.1927%2018.4083%2021.1927ZM13.6526%2016.2138C13.651%2015.943%2013.4301%2015.7248%2013.1592%2015.7264C12.8884%2015.7281%2012.6701%2015.949%2012.6718%2016.2199L12.6993%2020.7004C12.7009%2020.9713%2012.9218%2021.1895%2013.1927%2021.1879C13.4635%2021.1862%2013.6818%2020.9653%2013.6801%2020.6944L13.6526%2016.2138Z%22%20fill%3D%22white%22%2F%3E%3C%2Fg%3E%3Cdefs%3E%3Cfilter%20id%3D%22filter0_d_22298_271157%22%20x%3D%224.41016%22%20y%3D%228.3%22%20width%3D%2221.7305%22%20height%3D%2220.0517%22%20filterUnits%3D%22userSpaceOnUse%22%20color-interpolation-filters%3D%22sRGB%22%3E%3CfeFlood%20flood-opacity%3D%220%22%20result%3D%22BackgroundImageFix%22%2F%3E%3CfeColorMatrix%20in%3D%22SourceAlpha%22%20type%3D%22matrix%22%20values%3D%220%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%20127%200%22%20result%3D%22hardAlpha%22%2F%3E%3CfeOffset%20dy%3D%221%22%2F%3E%3CfeGaussianBlur%20stdDeviation%3D%221%22%2F%3E%3CfeColorMatrix%20type%3D%22matrix%22%20values%3D%220%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200.15%200%22%2F%3E%3CfeBlend%20mode%3D%22normal%22%20in2%3D%22BackgroundImageFix%22%20result%3D%22effect1_dropShadow_22298_271157%22%2F%3E%3CfeBlend%20mode%3D%22normal%22%20in%3D%22SourceGraphic%22%20in2%3D%22effect1_dropShadow_22298_271157%22%20result%3D%22shape%22%2F%3E%3C%2Ffilter%3E%3C%2Fdefs%3E%3C%2Fsvg%3E") 16 16,auto`;
// 插入文本的鼠标状态
export const MovecanvasOfInsertText = `url("data:image/svg+xml;base64,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") 16 16,auto`;
//插入frame的鼠标状态
export const MovecanvasOfInsertFrame = `url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMyAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWx0ZXI9InVybCgjYSkiPjxtYXNrIGlkPSJiIiBtYXNrVW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4PSI3LjUiIHk9IjgiIHdpZHRoPSIxNyIgaGVpZ2h0PSIxNyIgZmlsbD0iIzAwMCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTcuNSA4aDE3djE3aC0xN3oiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE2LjUgOXY3aC0xVjl6bS0xIDh2LTFoLTd2MXptMSAwdi0xaDd2MXptMCAwaC0xdjdoMXoiLz48L21hc2s+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNi41IDl2N2gtMVY5em0tMSA4di0xaC03djF6bTEgMHYtMWg3djF6bTAgMGgtMXY3aDF6IiBmaWxsPSIjMUExQTFBIi8+PHBhdGggZD0iTTE2LjUgOWgxYTEgMSAwIDAgMC0xLTF6bS0xIDBWOGExIDEgMCAwIDAtMSAxem0tNyA3di0xYTEgMSAwIDAgMC0xIDF6bTAgMWgtMWExIDEgMCAwIDAgMSAxem0xNS0xaDFhMSAxIDAgMCAwLTEtMXptMCAxdjFhMSAxIDAgMCAwIDEtMXptLTggN2gtMWExIDEgMCAwIDAgMSAxem0xIDB2MWExIDEgMCAwIDAgMS0xem0xLThWOWgtMnY3em0tMiAxaDF2LTJoLTF6bS0xLTh2N2gyVjl6bTItMWgtMXYyaDF6bTAgOXYtMWgtMnYxem0tMS0yaC03djJoN3ptLTggMXYxaDJ2LTF6bTEgMmg3di0yaC03em03LTJ2MWgydi0xem04LTFoLTd2Mmg3em0xIDJ2LTFoLTJ2MXptLTggMWg3di0yaC03em0wLTJoLTF2Mmgxem0tMiAxdjdoMnYtN3ptMSA4aDF2LTJoLTF6bTItMXYtN2gtMnY3eiIgZmlsbD0iI2ZmZiIgbWFzaz0idXJsKCNiKSIvPjwvZz48ZGVmcz48ZmlsdGVyIGlkPSJhIiB4PSI1LjUiIHk9IjciIHdpZHRoPSIyMSIgaGVpZ2h0PSIyMSIgZmlsdGVyVW5pdHM9InVzZXJTcGFjZU9uVXNlIiBjb2xvci1pbnRlcnBvbGF0aW9uLWZpbHRlcnM9InNSR0IiPjxmZUZsb29kIGZsb29kLW9wYWNpdHk9IjAiIHJlc3VsdD0iQmFja2dyb3VuZEltYWdlRml4Ii8+PGZlQ29sb3JNYXRyaXggaW49IlNvdXJjZUFscGhhIiB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDEyNyAwIiByZXN1bHQ9ImhhcmRBbHBoYSIvPjxmZU9mZnNldCBkeT0iMSIvPjxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjEiLz48ZmVDb2xvck1hdHJpeCB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAuMjUgMCIvPjxmZUJsZW5kIGluMj0iQmFja2dyb3VuZEltYWdlRml4IiByZXN1bHQ9ImVmZmVjdDFfZHJvcFNoYWRvd18yODU5XzE4Nzg1MyIvPjxmZUJsZW5kIGluPSJTb3VyY2VHcmFwaGljIiBpbjI9ImVmZmVjdDFfZHJvcFNoYWRvd18yODU5XzE4Nzg1MyIgcmVzdWx0PSJzaGFwZSIvPjwvZmlsdGVyPjwvZGVmcz48L3N2Zz4=") 16 16,auto`;
// 默认主要颜色
export const primaryColor = "#3549ff";

export const RenderCursor: Cursor & {
  mousedown: string;
  insertText: string;
  insertFrame: string;
} = {
  move: CanvasDefaultCursor,
  default: CanvasDefaultCursor,
  hover: CanvasDefaultCursor,
  mousedown: MovecanvasOfMouseDown,
  insertText: MovecanvasOfInsertText,
  insertFrame: MovecanvasOfInsertFrame,
};

export const visibleControls = {
  mtr: true,
  ml: false,
  mr: false,
  mt: false,
  mb: false,
};

/**
 * 自定义控制渲染
 * @param ctx
 * @param left
 * @param top
 */
function customControlRender(
  ctx: CanvasRenderingContext2D,
  left: number,
  top: number
) {
  ctx.save();
  ctx.globalAlpha = 0;
  ctx.beginPath();
  ctx.arc(left, top, 6, 0, 2 * Math.PI);
  ctx.fillStyle = "#ffffff";
  ctx.strokeStyle = defaultSelectionColor;
  ctx.lineWidth = 1;
  ctx.fill();
  ctx.stroke();
  ctx.restore();
}

export const controlMouseDownHandler: ControlActionHandler = (
  _eventData,
  transformData,
  _x,
  _y
) => {
  Object.keys(transformData.target.controls).forEach((key) => {
    transformData.target.controls[
      key as keyof typeof transformData.target.controls
    ].isTransparent = true;
  });
  return true;
};
export const controlMouseUpHandler: ControlActionHandler = (
  _eventData,
  transformData,
  _x,
  _y
) => {
  Object.keys(transformData.target.controls).forEach((key) => {
    transformData.target.controls[
      key as keyof typeof transformData.target.controls
    ].isTransparent = false;
  });
  return true;
};

/**
 * 创建旋转控制
 * @param x
 * @param y
 * @param offsetX
 * @param offsetY
 * @param cursorStyle
 * @returns
 */
export const createRotateControl = (
  x: number,
  y: number,
  offsetX: number,
  offsetY: number,
  cursorStyle: string | CursorStyleHandler
) => {
  return new Control({
    x,
    y,
    cursorStyle: typeof cursorStyle === "string" ? cursorStyle : undefined,
    offsetY,
    offsetX,
    actionHandler: controlsUtils.rotationWithSnapping,
    cursorStyleHandler:
      typeof cursorStyle === "string" ? undefined : cursorStyle,
    actionName: "rotate",
    render: customControlRender,
    mouseUpHandler: controlMouseUpHandler,
    mouseDownHandler: controlMouseDownHandler,
  });
};

/**
 * 自定义缩放控制渲染
 * @param ctx
 * @param left
 * @param top
 */
export const customScaleControlRender = (
  ctx: CanvasRenderingContext2D,
  left: number,
  top: number,
  _styleOverride: ControlRenderingStyleOverride | undefined,
  fabricObject: InteractiveFabricObject
) => {
  const angle = fabricObject.getTotalAngle();
  const controls = fabricObject.controls;
  const xSize = 8,
    xSizeBy2 = xSize / 2;
  const isTransparent = Object.keys(controls).some((key) => {
    return controls[key as keyof typeof controls].isTransparent;
  });
  ctx.save();

  ctx.globalAlpha = 1;
  if (isTransparent) {
    ctx.globalAlpha = 0.1;
  }
  ctx.translate(left, top);
  ctx.rotate(angle * (Math.PI / 180));
  ctx.fillStyle = "#ffffff";
  const borderColor = getSelectionColor(
    fabricObject.get("_name_") as ElementName
  );
  ctx.strokeStyle = borderColor;
  ctx.lineWidth = 1;
  ctx.beginPath();

  ctx.arc(0, 0, xSizeBy2, 0, Math.PI * 2);
  ctx.fill();
  ctx.stroke();

  ctx.restore();
};

InteractiveFabricObject.ownDefaults = {
  ...InteractiveFabricObject.ownDefaults,
  padding: 0,
  borderColor: defaultSelectionColor,
  transparentCorners: false,
  borderScaleFactor: 1,
  borderOpacityWhenMoving: 1,
};

export const ownDefaultsMouseStyle: MouseStyle = {
  move: RenderCursor.default,
  defaults: RenderCursor.default,
  hover: RenderCursor.default,
  mousedown: "pointer",
  rotateHandler: {
    mtlHandler: RotateTopLeftCursorBySourceHandler,
    mtrHandler: RotateTopRightCursorBySourceHandler,
    mblHandler: RotateBottomLeftCursorBySourceHandler,
    mbrHandler: RotateBottomRightCursorBySourceHandler,
    mtmHandler: RotateTopLeftCursorBySourceHandler,
    mbmHandler: RotateTopLeftCursorBySourceHandler,
    mlmHandler: RotateTopLeftCursorBySourceHandler,
    mrmHandler: RotateTopLeftCursorBySourceHandler,
  },
  scaleHandler: {
    blHandler: ScaleBottomLeftCursorBySourceHandler,
    brHandler: ScaleBottomRightCursorBySourceHandler,
    tlHandler: ScaleTopLeftCursorBySourceHandler,
    trHandler: ScaleTopRightCursorBySourceHandler,
  },
};

export const propertiesToInclude = [
  "_name_",
  "_id_",
  "_old_prompt_",
  "_new_prompt_",
  "_message_id_",
  "_loading_",
  "_parent_id_",
  "_custom_data_history_",
  "_custom_data_",
  "type",
];

export const baseProperties = {
  _id_: "",
  _parent_id_: "",
  _name_: "",
  _loading_element_: false,
  _loading_: false,
  erasable: false,
  _message_id_: "",
  _old_prompt_: "",
  _new_prompt_: "",
  _font_url_: "",
  _custom_data_history_: {},
  _custom_data_: {},
};

export const ImageProperties = {
  ...baseProperties,
  subTargetCheck: false,
  _name_: ElementName.IMAGE,
};

export const VideoProperties = {
  ...baseProperties,
  subTargetCheck: false,
  _name_: ElementName.VIDEO,
};

export const LoadingGroupProperties = {
  ...baseProperties,
  _name_: ElementName.LOADING_GROUP,
  _loading_element_: true,
};

export const LoadingRectProperties = {
  ...baseProperties,
  _name_: ElementName.LOADING_RECT,
  _loading_element_: true,
};

export const ErrorProperties = {
  ...baseProperties,
  _name_: ElementName.ERROR,
};

export const LoadingTextProperties = {
  ...baseProperties,
  _name_: ElementName.LOADING_TEXT,
  _loading_element_: true,
};

export const ContainerProperties = {
  ...baseProperties,
  _name_: ElementName.CONTAINER,
  interactive: false,
  subTargetCheck: false,
};

// 新元素的偏移量
export const newElementOffset = 10;

// 画布默认发出警告的图片承载数量
export const defaultWarningImageCount = 100;

// 画布默认最小缩放比例
export const defaultMinZoom = 0.03;
// 画布默认最大缩放比例
export const defaultMaxZoom = 20;

Canvas.ownDefaults.preserveObjectStacking = true;
Canvas.ownDefaults.renderOnAddRemove = true;
Canvas.ownDefaults.webgl = true;
FabricObject.ownDefaults.originX = "center";
FabricObject.ownDefaults.originY = "center";
