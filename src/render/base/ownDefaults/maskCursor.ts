
/**
 * 智能选择+ base64
 */
export const RenderMaskCursorIntelligentPlusByBase64 = `data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
/**
 * 智能选择- base64
 */
export const RenderMaskCursorIntelligentMinusByBase64 = `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMyIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDMzIDMyIiBmaWxsPSJub25lIj48ZyBmaWx0ZXI9InVybCgjYSkiPjxwYXRoIGQ9Ik02LjUxMSA0LjYxMmEuNTAzLjUwMyAwIDAgMSAuNzQyLS41NDRsMTQuOTk0IDguNjZhLjUwOC41MDggMCAwIDEtLjEwMi45MjNsLTcuMjc3IDIuMjU2YS41LjUgMCAwIDAtLjI3NC4yMDhsLTMuNjQyIDUuNjU0YS41MDEuNTAxIDAgMCAxLS45MTQtLjE3MnoiIGZpbGw9IiMxQTFBMUEiLz48cGF0aCBkPSJNNi45MDMgNC41M2EuMTA0LjEwNCAwIDAgMSAuMTUtLjExNWwxNC45OTQgOC42NTlhLjEuMSAwIDAgMSAuMDQzLjA0NC4xLjEgMCAwIDEgLjAxLjA2MS4xLjEgMCAwIDEtLjAyMy4wNTcuMS4xIDAgMCAxLS4wNS4wMzNsLTcuMjc4IDIuMjU2YS45LjkgMCAwIDAtLjQ5MS4zNzRsLTMuNjQzIDUuNjUzYS4xMDEuMTAxIDAgMCAxLS4xODUtLjAzNnoiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSIuOCIvPjwvZz48cGF0aCBkPSJNNi41MTEgNC42MTJhLjUwMy41MDMgMCAwIDEgLjc0Mi0uNTQ0bDE0Ljk5NCA4LjY2YS41MDguNTA4IDAgMCAxLS4xMDIuOTIzbC03LjI3NyAyLjI1NmEuNS41IDAgMCAwLS4yNzQuMjA4bC0zLjY0MiA1LjY1NGEuNTAxLjUwMSAwIDAgMS0uOTE0LS4xNzJ6IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iLjUiLz48ZyBmaWx0ZXI9InVybCgjYikiPjxtYXNrIGlkPSJjIiBtYXNrVW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4PSIxOS41IiB5PSIyMCIgd2lkdGg9IjciIGhlaWdodD0iMyIgZmlsbD0iIzAwMCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTE5LjUgMjBoN3YzaC03eiIvPjxwYXRoIGQ9Ik0yMC41IDIxaDV2MWgtNXoiLz48L21hc2s+PHBhdGggZD0iTTIwLjUgMjFoNXYxaC01eiIgZmlsbD0iIzFBMUExQSIvPjxwYXRoIGQ9Ik0yMC41IDIxdi0xaC0xdjF6bTUgMGgxdi0xaC0xem0wIDF2MWgxdi0xem0tNSAwaC0xdjFoMXptMCAwaDV2LTJoLTV6bTQtMXYxaDJ2LTF6bTEgMGgtNXYyaDV6bS00IDF2LTFoLTJ2MXoiIGZpbGw9IiNmZmYiIG1hc2s9InVybCgjYykiLz48L2c+PGRlZnM+PGZpbHRlciBpZD0iYSIgeD0iNC4xIiB5PSIyLjgiIHdpZHRoPSIyMC44IiBoZWlnaHQ9IjIyLjgiIGZpbHRlclVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgY29sb3ItaW50ZXJwb2xhdGlvbi1maWx0ZXJzPSJzUkdCIj48ZmVGbG9vZCBmbG9vZC1vcGFjaXR5PSIwIiByZXN1bHQ9IkJhY2tncm91bmRJbWFnZUZpeCIvPjxmZUNvbG9yTWF0cml4IGluPSJTb3VyY2VBbHBoYSIgdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAxMjcgMCIgcmVzdWx0PSJoYXJkQWxwaGEiLz48ZmVPZmZzZXQgZHk9IjEuMiIvPjxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjEuMiIvPjxmZUNvbG9yTWF0cml4IHZhbHVlcz0iMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMC4yNSAwIi8+PGZlQmxlbmQgaW4yPSJCYWNrZ3JvdW5kSW1hZ2VGaXgiIHJlc3VsdD0iZWZmZWN0MV9kcm9wU2hhZG93XzI4NTlfMTg3ODI2Ii8+PGZlQmxlbmQgaW49IlNvdXJjZUdyYXBoaWMiIGluMj0iZWZmZWN0MV9kcm9wU2hhZG93XzI4NTlfMTg3ODI2IiByZXN1bHQ9InNoYXBlIi8+PC9maWx0ZXI+PGZpbHRlciBpZD0iYiIgeD0iMTcuNSIgeT0iMTkiIHdpZHRoPSIxMSIgaGVpZ2h0PSI3IiBmaWx0ZXJVbml0cz0idXNlclNwYWNlT25Vc2UiIGNvbG9yLWludGVycG9sYXRpb24tZmlsdGVycz0ic1JHQiI+PGZlRmxvb2QgZmxvb2Qtb3BhY2l0eT0iMCIgcmVzdWx0PSJCYWNrZ3JvdW5kSW1hZ2VGaXgiLz48ZmVDb2xvck1hdHJpeCBpbj0iU291cmNlQWxwaGEiIHZhbHVlcz0iMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMTI3IDAiIHJlc3VsdD0iaGFyZEFscGhhIi8+PGZlT2Zmc2V0IGR5PSIxIi8+PGZlR2F1c3NpYW5CbHVyIHN0ZERldmlhdGlvbj0iMSIvPjxmZUNvbG9yTWF0cml4IHZhbHVlcz0iMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMC4yNSAwIi8+PGZlQmxlbmQgaW4yPSJCYWNrZ3JvdW5kSW1hZ2VGaXgiIHJlc3VsdD0iZWZmZWN0MV9kcm9wU2hhZG93XzI4NTlfMTg3ODI2Ii8+PGZlQmxlbmQgaW49IlNvdXJjZUdyYXBoaWMiIGluMj0iZWZmZWN0MV9kcm9wU2hhZG93XzI4NTlfMTg3ODI2IiByZXN1bHQ9InNoYXBlIi8+PC9maWx0ZXI+PC9kZWZzPjwvc3ZnPg==`
/**
 * 框选+ base64
 */
export const RenderMaskCursorRectPlusByBase64 = `data:image/svg+xml;base64,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`
/**
 * 框选- base64
 */
export const RenderMaskCursorRectMinusByBase64 = `data:image/svg+xml;base64,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`
/**
 * 圈索+
 */
export const RenderMaskCursorLassoPlusByBase64 = `data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
/**
 * 圈索- 
 */
export const RenderMaskCursorLassoMinusByBase64 = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjUiIGhlaWdodD0iMzEiIHZpZXdCb3g9IjAgMCAyNSAzMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWx0ZXI9InVybCgjYSkiPjxwYXRoIGQ9Ik0xOC43NSAyNC42N3EuMzgyLS4yMTkuNzI1LS40NzVjMi44MjUtMi4xMDkgMy43MTYtNi4wNjEgMS42MTYtOC44NzNzLTYuMTQyLTMuMDgtOC45NjctLjk3MWMtMi42MjIgMS45NTctMy41NzggNS41MDQtMi4wMjIgOC4yNTJhNiA2IDAgMCAxLS40NzEtLjIyNCAxLjM3NSAxLjM3NSAwIDEgMC0xLjI5MiAyLjQyOGMxLjcwNS45MDcgMy43ODMgMS4yNDIgNS43MiAxLjEzOSAxLjMxNy42NDggMi45MzggMS4yMDMgNC44NjMgMS41NjZhMS4zNzUgMS4zNzUgMCAwIDAgLjUxLTIuNzAycS0uMzUtLjA2Ni0uNjgyLS4xNCIgZmlsbD0iI2ZmZiIgZmlsbC1vcGFjaXR5PSIuMDEiLz48L2c+PGcgZmlsdGVyPSJ1cmwoI2IpIj48cGF0aCBkPSJNMi41MSAyLjUxYy0uMDczLS4zNTcuMjk2LS42MzQuNjAyLS40NTNsMTIuMTgzIDcuMjE2Yy4zMS4xODQuMjU4LjY2MS0uMDgzLjc3bC01LjkxMyAxLjg4YS40LjQgMCAwIDAtLjIyMy4xNzNsLTIuOTYgNC43MTJhLjQwNC40MDQgMCAwIDEtLjc0MS0uMTQ0eiIgZmlsbD0iIzFBMUExQSIvPjxwYXRoIGQ9Im0xNS4wOSA5LjYxNy4wMDYuMDA0LjAwMi4wMDJxLjAwMi4wMDUuMDAyLjAybC0uMDA1LjAxN2gtLjAwMWwtLjAwMy4wMDEtNS45MTMgMS44OGEuOC44IDAgMCAwLS4zNTUuMjNsLS4wODYuMTEzLTIuOTU5IDQuNzFxLS4wMDEuMDA0LS4wMDQuMDA1aC0uMDAzdi0uMDAybC0uMDA0LS4wMTJMMi45IDIuNDMxdi0uMDJsLjAwNy0uMDF6IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iLjgiLz48L2c+PHBhdGggZD0iTTIuNTEgMi41MWMtLjA3My0uMzU3LjI5Ni0uNjM0LjYwMi0uNDUzbDEyLjE4MyA3LjIxNmMuMzEuMTg0LjI1OC42NjEtLjA4My43N2wtNS45MTMgMS44OGEuNC40IDAgMCAwLS4yMjMuMTczbC0yLjk2IDQuNzEyYS40MDQuNDA0IDAgMCAxLS43NDEtLjE0NHoiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSIuNSIvPjxlbGxpcHNlIGN4PSIxNSIgY3k9IjE5IiByeD0iNC41IiByeT0iNSIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik04Ljk4NCAyMy41OTNjMi45IDEuNTQ0IDcuMzU0IDEuMjI5IDkuNjY4LS40OTlzMi45MTMtNC44NCAxLjMzNy02Ljk1LTQuNzI4LTIuNDItNy4wNDItLjY5MWMtMi4zMTQgMS43MjctMi45MTMgNC44MzktMS4zMzcgNi45NDkuOTUyIDEuMjc2IDMuNDAyIDIuOTc0IDcuNTY2IDMuNzYiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz48cGF0aCBkPSJNOC45ODQgMjMuNTkzYzIuOSAxLjU0NCA3LjM1NCAxLjIyOSA5LjY2OC0uNDk5czIuOTEzLTQuODQgMS4zMzctNi45NS00LjcyOC0yLjQyLTcuMDQyLS42OTFjLTIuMzE0IDEuNzI3LTIuOTEzIDQuODM5LTEuMzM3IDYuOTQ5Ljk1MiAxLjI3NiAzLjQwMiAyLjk3NCA3LjU2NiAzLjc2IiBzdHJva2U9IiMxQTFBMUEiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPjxwYXRoIGZpbGw9IiMxQTFBMUEiIGQ9Ik0xMy41IDE5aDV2MWgtNXoiLz48ZGVmcz48ZmlsdGVyIGlkPSJhIiB4PSI1LjYwOSIgeT0iMTEuOTYxIiB3aWR0aD0iMTguNjM4IiBoZWlnaHQ9IjE4LjU3NSIgZmlsdGVyVW5pdHM9InVzZXJTcGFjZU9uVXNlIiBjb2xvci1pbnRlcnBvbGF0aW9uLWZpbHRlcnM9InNSR0IiPjxmZUZsb29kIGZsb29kLW9wYWNpdHk9IjAiIHJlc3VsdD0iQmFja2dyb3VuZEltYWdlRml4Ii8+PGZlQ29sb3JNYXRyaXggaW49IlNvdXJjZUFscGhhIiB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDEyNyAwIiByZXN1bHQ9ImhhcmRBbHBoYSIvPjxmZU9mZnNldCBkeT0iMSIvPjxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjEiLz48ZmVDb2xvck1hdHJpeCB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAuMjUgMCIvPjxmZUJsZW5kIGluMj0iQmFja2dyb3VuZEltYWdlRml4IiByZXN1bHQ9ImVmZmVjdDFfZHJvcFNoYWRvd18yODU5XzE4NzcxMyIvPjxmZUJsZW5kIGluPSJTb3VyY2VHcmFwaGljIiBpbjI9ImVmZmVjdDFfZHJvcFNoYWRvd18yODU5XzE4NzcxMyIgcmVzdWx0PSJzaGFwZSIvPjwvZmlsdGVyPjxmaWx0ZXIgaWQ9ImIiIHg9Ii4xIiB5PSIuOCIgd2lkdGg9IjE3LjgiIGhlaWdodD0iMTkuOCIgZmlsdGVyVW5pdHM9InVzZXJTcGFjZU9uVXNlIiBjb2xvci1pbnRlcnBvbGF0aW9uLWZpbHRlcnM9InNSR0IiPjxmZUZsb29kIGZsb29kLW9wYWNpdHk9IjAiIHJlc3VsdD0iQmFja2dyb3VuZEltYWdlRml4Ii8+PGZlQ29sb3JNYXRyaXggaW49IlNvdXJjZUFscGhhIiB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDEyNyAwIiByZXN1bHQ9ImhhcmRBbHBoYSIvPjxmZU9mZnNldCBkeT0iMS4yIi8+PGZlR2F1c3NpYW5CbHVyIHN0ZERldmlhdGlvbj0iMS4yIi8+PGZlQ29sb3JNYXRyaXggdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwLjI1IDAiLz48ZmVCbGVuZCBpbjI9IkJhY2tncm91bmRJbWFnZUZpeCIgcmVzdWx0PSJlZmZlY3QxX2Ryb3BTaGFkb3dfMjg1OV8xODc3MTMiLz48ZmVCbGVuZCBpbj0iU291cmNlR3JhcGhpYyIgaW4yPSJlZmZlY3QxX2Ryb3BTaGFkb3dfMjg1OV8xODc3MTMiIHJlc3VsdD0ic2hhcGUiLz48L2ZpbHRlcj48L2RlZnM+PC9zdmc+`

/**
 * 涂抹+ function call
 * @param {number} width
 */
export const RenderMaskCursorPathPlus = (width: number) => {
  const svg = `
  <svg width="${width}" height="${width}" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#a)">
      <circle cx="16.5" cy="16" r="12.5" fill="#fff" fill-opacity=".1"/>
      <circle cx="16.5" cy="16" r="12.25" stroke="#000" stroke-width=".5"/>
    </g>
    <mask id="b" maskUnits="userSpaceOnUse" x="12" y="11.5" width="9" height="9" fill="#000">
      <path fill="#fff" d="M12 11.5h9v9h-9z"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M16.7 12.75a.25.25 0 1 0-.5 0v3.05h-2.95a.25.25 0 0 0 0 .5h2.95v2.95a.25.25 0 1 0 .5 0V16.3h3.05a.25.25 0 1 0 0-.5H16.7z"/>
    </mask>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M16.7 12.75a.25.25 0 1 0-.5 0v3.05h-2.95a.25.25 0 0 0 0 .5h2.95v2.95a.25.25 0 1 0 .5 0V16.3h3.05a.25.25 0 1 0 0-.5H16.7z" fill="#000"/>
    <path d="M16.2 15.8v.5a.5.5 0 0 0 .5-.5zm0 .5h.5a.5.5 0 0 0-.5-.5zm.5 0v-.5a.5.5 0 0 0-.5.5zm0-.5h-.5a.5.5 0 0 0 .5.5zm-.25-2.8a.25.25 0 0 1-.25-.25h1a.75.75 0 0 0-.75-.75zm.25-.25a.25.25 0 0 1-.25.25v-1a.75.75 0 0 0-.75.75zm0 3.05v-3.05h-1v3.05zm-3.45.5h2.95v-1h-2.95zm.25-.25a.25.25 0 0 1-.25.25v-1a.75.75 0 0 0-.75.75zm-.25-.25a.25.25 0 0 1 .25.25h-1c0 .414.336.75.75.75zm2.95 0h-2.95v1h2.95zm.5 3.45V16.3h-1v2.95zm-.25-.25a.25.25 0 0 1 .25.25h-1c0 .414.336.75.75.75zm-.25.25a.25.25 0 0 1 .25-.25v1a.75.75 0 0 0 .75-.75zm0-2.95v2.95h1V16.3zm3.55-.5H16.7v1h3.05zm-.25.25a.25.25 0 0 1 .25-.25v1a.75.75 0 0 0 .75-.75zm.25.25a.25.25 0 0 1-.25-.25h1a.75.75 0 0 0-.75-.75zm-3.05 0h3.05v-1H16.7zm-.5-3.55v3.05h1v-3.05z" fill="#fff" mask="url(#b)"/>
    <defs>
      <filter id="a" x="2" y="2.5" width="29" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="1"/>
        <feGaussianBlur stdDeviation="1"/>
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
        <feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/>
        <feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
        <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feMorphology radius="2" in="SourceAlpha" result="effect2_innerShadow"/>
        <feOffset/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
        <feBlend in2="shape" result="effect2_innerShadow"/>
      </filter>
    </defs>
  </svg>`;

  const base64 = window.btoa(unescape(encodeURIComponent(svg)));
  const dataUrl = `url("data:image/svg+xml;base64,${base64}") ${width / 2} ${width / 2}, auto`;
  return dataUrl
}

/**
 * 涂抹+ function call
 * @param {number} width
 */
export const RenderMaskCursorPathMinus = (width: number) => {
  const svg = `
    <svg width="${width}" height="${width}" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_di_2859_187750)">
    <circle cx="16.5" cy="16" r="12.5" fill="white" fill-opacity="0.1"/>
    <circle cx="16.5" cy="16" r="12.25" stroke="black" stroke-width="0.5"/>
    </g>
    <mask id="path-3-outside-1_2859_187750" maskUnits="userSpaceOnUse" x="12" y="14.8" width="9" height="2" fill="black">
    <rect fill="white" x="12" y="14.8" width="9" height="2"/>
    <path d="M13 16.05C13 15.912 13.1119 15.8 13.25 15.8H19.75C19.8881 15.8 20 15.912 20 16.05C20 16.1881 19.8881 16.3 19.75 16.3H13.25C13.1119 16.3 13 16.1881 13 16.05Z"/>
    </mask>
    <path d="M13 16.05C13 15.912 13.1119 15.8 13.25 15.8H19.75C19.8881 15.8 20 15.912 20 16.05C20 16.1881 19.8881 16.3 19.75 16.3H13.25C13.1119 16.3 13 16.1881 13 16.05Z" fill="black"/>
    <path d="M13.25 16.3H19.75V15.3H13.25V16.3ZM19.75 15.8H13.25V16.8H19.75V15.8ZM13.25 15.8C13.3881 15.8 13.5 15.912 13.5 16.05H12.5C12.5 16.4643 12.8358 16.8 13.25 16.8V15.8ZM19.5 16.05C19.5 15.912 19.6119 15.8 19.75 15.8V16.8C20.1642 16.8 20.5 16.4643 20.5 16.05H19.5ZM19.75 16.3C19.6119 16.3 19.5 16.1881 19.5 16.05H20.5C20.5 15.6358 20.1642 15.3 19.75 15.3V16.3ZM13.25 15.3C12.8358 15.3 12.5 15.6358 12.5 16.05H13.5C13.5 16.1881 13.3881 16.3 13.25 16.3V15.3Z" fill="white" mask="url(#path-3-outside-1_2859_187750)"/>
    <defs>
    <filter id="filter0_di_2859_187750" x="2" y="2.5" width="29" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="1"/>
    <feGaussianBlur stdDeviation="1"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2859_187750"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2859_187750" result="shape"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect2_innerShadow_2859_187750"/>
    <feOffset/>
    <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
    <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
    <feBlend mode="normal" in2="shape" result="effect2_innerShadow_2859_187750"/>
    </filter>
    </defs>
    </svg>
`;

  const base64 = window.btoa(unescape(encodeURIComponent(svg)));
  const dataUrl = `url("data:image/svg+xml;base64,${base64}") ${width / 2} ${width / 2}, auto`;
  return dataUrl
}






export const RenderMaskCursor = {
  /**
   * 智能选择+
   * */
  intelligentPlus: `url("${RenderMaskCursorIntelligentPlusByBase64}") 0 0,auto`,
  /**
   * 智能选择-
   * */
  intelligentMinus: `url("${RenderMaskCursorIntelligentMinusByBase64}") 0 0,auto`,
  /**
   * 框选+
   */
  rectPlus: `url("${RenderMaskCursorRectPlusByBase64}") 16 16, auto`,
  /**
 * 框选-
 */
  rectMinus: `url("${RenderMaskCursorRectMinusByBase64}") 16 16, auto`,
  /**
   * 套索+
   */
  lassoPlus: `url("${RenderMaskCursorLassoPlusByBase64}") 0 0, auto`,
  /**
   * 套索-
   */
  lassoMinus: `url("${RenderMaskCursorLassoMinusByBase64}") 0 0, auto`,
  /**
   * 涂抹+
   */
  pathPlus: RenderMaskCursorPathPlus,
  /**
   * 涂抹-
   */
  pathMinus: RenderMaskCursorPathMinus,
}