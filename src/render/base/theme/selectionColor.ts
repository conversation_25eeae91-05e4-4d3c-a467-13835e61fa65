import { ElementName } from "../../utils";
import { SelectionColor } from "./types";

export const defaultSelectionColor = '#43464D' // 默认
export const defaultMultiSelectionColor = '#3549ff' // 多选
export const defaultFrameSelectionColor = '#53F6B4' // 框架
export const defaultImageSelectionColor = '#F761FF' // 图片
export const defaultTextSelectionColor = '#43464D' // 文本
export const defaultContainerSelectionColor = '#43464D' // 组合
export const selectionColors: SelectionColor[] = [
    {
        type: ElementName.FRAME,
        color: defaultFrameSelectionColor,
    },
    {
        type: ElementName.IMAGE,
        color: defaultImageSelectionColor,
    },
    {
        type: ElementName.CONTAINER,
        color: defaultContainerSelectionColor,
    },
    {
        type: ElementName.TEXT,
        color: defaultTextSelectionColor,
    },
]

/**
 * 设置选中的颜色设置选中的颜色
 * @param type 元素类型元素类型
 * @param color 颜色颜色
 */
export const setSelectionColor = (type: ElementName, color: string) => {
    const target = selectionColors.find(item => item.type === type)
    if (target) {
        selectionColors.splice(selectionColors.indexOf(target), 1, {
            type,
            color,
        })
    } else {
        selectionColors.push({
            type,
            color,
        })
    }
}

/**
 * 获取选中的颜色
 * @param type 元素类型
 * @returns 颜色颜色
 */
export const getSelectionColor = (type: ElementName) => {
    const target = selectionColors.find(item => item.type === type)
    if (target) {
        return target.color
    }
    return defaultSelectionColor
}
