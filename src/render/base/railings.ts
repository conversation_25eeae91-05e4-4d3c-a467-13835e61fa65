import {
  ActiveSelection,
  Canvas,
  CanvasEvents,
  TPointerEvent,
  TPointerEventInfo,
} from 'fabric';
import { debounce } from 'lodash-es'
import { Render } from '../render';
import { defaultSelectionColor } from './theme/selectionColor';

export class Railings {
  private _events: Record<
    string,
    Array<(e: TPointerEventInfo<TPointerEvent>) => void>
  > = {};
  private continueUpdateRailing: boolean = false;
  private disableClearContext: boolean = false;
  private _FC: Canvas;
  private railing: {
    left: number;
    top: number;
    width: number;
    height: number;
  } = {
      left: 0,
      top: 0,
      width: 0,
      height: 0,
    }
  private showRailing: boolean = true;
  constructor(render: Render, showRailing: boolean = true) {
    this.showRailing = showRailing;
    this._FC = render._FC;
    this._events = {
      'selection:created': [this._onSelectionCreated],
      'selection:updated': [this._onSelectionUpdated],
      'selection:cleared': [this._onSelectionCleared],
      'object:scaling': [this.drawRailingHandler],
      'object:rotating': [this.drawRailingHandler],
      'object:moving': [this.drawRailingCallback],
      'object:modified': [this.drawRailingCallback],
      'history:changed': [this.drawRailingHandler],
      'viewport:translate': [this.drawRailingHandler],
      'viewport:zoom': [this.drawRailingHandler],
    };
    this._bindEvents();
  }

  public _destroy = () => {
    Object.entries(this._events).forEach(([event, handlers]) =>
      handlers.forEach((handler) =>
        this._FC.off(event as keyof CanvasEvents, handler)
      )
    );
  };

  private _bindEvents(): void {
    Object.entries(this._events).forEach(([event, handlers]) =>
      handlers.forEach((handler) =>
        this._FC.on(event as keyof CanvasEvents, handler)
      )
    );
  }

  private setDisableClearContext = (value: boolean) => {
    this.disableClearContext = value;
  }
  private drawRailingCallback = () => {
    const ctx = this._FC.contextTop;
    if (this.disableClearContext) return;
    this._FC.clearContext(ctx);
    this.drawRailing()
  }

  public drawRailingHandler = () => {
    const ctx = this._FC.contextTop;
    // if (this.disableClearContext) return;
    this._FC.clearContext(ctx);
    const as = this._FC.getActiveObject();
    if (!as || !(as instanceof ActiveSelection)) return;
    const viewPort = this._FC.viewportTransform;
    const coords = as.getCoords();
    const sceneX = Math.min(...coords.map(item => item.x));
    const sceneY = Math.min(...coords.map(item => item.y));
    const sceneWidth = Math.max(...coords.map(item => item.x)) - sceneX;
    const sceneHeight = Math.max(...coords.map(item => item.y)) - sceneY;
    this.railing = {
      left: sceneX,
      top: sceneY,
      width: sceneWidth,
      height: sceneHeight,
    };
    if (this.showRailing) {
      const positions = coords.map(item => item.transform(viewPort));
      const minX = Math.min(...positions.map(item => item.x));
      const minY = Math.min(...positions.map(item => item.y));
      const width = Math.max(...positions.map(item => item.x)) - minX;
      const height = Math.max(...positions.map(item => item.y)) - minY;
      ctx.save();
      ctx.beginPath();
      const viewTransform = ctx.getTransform();
      ctx.lineWidth = 1 / (viewTransform.a * window.devicePixelRatio);
      ctx.strokeStyle = defaultSelectionColor;
      ctx.rect(minX, minY, width, height);
      ctx.stroke();
      ctx.restore();
    }
  }

  private drawRailing = debounce(this.drawRailingHandler, 100)

  private _onSelectionCreated = (_e: TPointerEventInfo<TPointerEvent>) => {
    if (this.continueUpdateRailing) return;
    const objects = this._FC.getActiveObjects();
    this.setDisableClearContext(objects.length < 2);
    this.drawRailing();
  };
  private _onSelectionUpdated = (_e: TPointerEventInfo<TPointerEvent>) => {
    if (this.continueUpdateRailing) return;
    const objects = this._FC.getActiveObjects();
    this.setDisableClearContext(objects.length < 2);
    this.drawRailing();
  };
  private _onSelectionCleared = (e: any) => {
    if (this.continueUpdateRailing) return;
    if (e.deselected.length < 2) return;
    this.drawRailing();
  };

  public get RailingPosition() {
    return {
      left: this.railing.left,
      top: this.railing.top,
      right: this.railing.left + this.railing.width,
      bottom: this.railing.top + this.railing.height,
      centerX: this.railing.left + this.railing.width / 2,
      centerY: this.railing.top + this.railing.height / 2,
    };
  }
  public setContinueUpdateRailing = (value: boolean) => {
    this.continueUpdateRailing = value;
  };
}
