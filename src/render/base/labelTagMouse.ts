import { <PERSON>vas, Point, TPointerEventInfo, TPointerEvent } from 'fabric';
import { Frame } from './package/frame/frame';

export class LabelTagMouse {
    private isDraggingLabel: boolean = false;
    private _lastPointer: Point | null = null;
    private _lastFrame: Frame | null = null;
    constructor(private __FC: Canvas) {
        this.init();
    }

    private init = () => {
        this.__FC.on('mouse:down', this._onMouseDown);
        this.__FC.on('mouse:move', this._onMouseMove);
        this.__FC.on('mouse:up', this._onMouseUp);
    }
    private unInit = () => {
        this.__FC.off('mouse:down', this._onMouseDown);
        this.__FC.off('mouse:move', this._onMouseMove);
        this.__FC.off('mouse:up', this._onMouseUp);
    }


    private _onMouseDown = (event: TPointerEventInfo<TPointerEvent> & { alreadySelected?: boolean }) => {
        const pointer = this.__FC.getScenePoint(event.e as PointerEvent);
        const canvasAllObjects = this.__FC.getObjects();
        const object = canvasAllObjects
        .filter(obj => obj.containsPoint(pointer))
        .filter(obj => !(obj instanceof Frame));
        const frames = canvasAllObjects.filter(obj => obj instanceof Frame);
        const targetObjects = [...object, ...frames];
        targetObjects.sort((a, b) => canvasAllObjects.indexOf(b) - canvasAllObjects.indexOf(a));
        for (const currentObject of targetObjects) {
            if (!(currentObject instanceof Frame)) continue
            const frame = currentObject as Frame;
            const labelBounds = frame.getLabelBounds();
            if (!labelBounds) continue
            if (!frame.isPointInLabel(pointer)) continue
            this._lastFrame = frame;
            this.__FC.setActiveObject(frame);
            this.__FC.requestRenderAll();
            this._lastPointer = pointer;
            this.isDraggingLabel = true;
            this.__FC.selection = false;
            (event.e as PointerEvent).preventDefault();
            (event.e as PointerEvent).stopPropagation();
        }
    }
    private _onMouseMove = (event: TPointerEventInfo<PointerEvent>) => {
        if (!this.isDraggingLabel || !this._lastPointer) return
        const currentPointer = this.__FC.getScenePoint(event.e);
        const dx = currentPointer.x - this._lastPointer.x;
        const dy = currentPointer.y - this._lastPointer.y;
        this._lastFrame?.set('left', this._lastFrame.left + dx);
        this._lastFrame?.set('top', this._lastFrame.top + dy);
        this._lastFrame?.setCoords();
        this._lastPointer = currentPointer;
        this.__FC.renderAll();
        event.e.preventDefault();
        event.e.stopPropagation();
    }

    private _onMouseUp = () => {
        if (!this.isDraggingLabel) return
        this.isDraggingLabel = false;
        this.__FC.selection = true;
        this._lastFrame = null;
        this._lastPointer = null;
        this.__FC.requestRenderAll();
    }

    public _destroy = () => {
        this.unInit();
    }
}
