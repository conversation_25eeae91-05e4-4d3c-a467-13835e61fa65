import { Canvas, FabricObject, Point, TPointerEventInfo, TPointerEvent } from 'fabric';


type Options = {
    minZoom: number
    maxZoom: number
}
export class Wheel {
    private _lockPointer: Point | null = null;
    private platform: string = '';
    constructor(private __FC: Canvas, private options: Options) {
        this.init();
        this.initPlatform();
    }

    private initPlatform = () => {
        const userAgent = window.navigator.userAgent.toLowerCase();
      if (userAgent.includes('mac')) {
        this.platform = 'mac';
      } else if (userAgent.includes('win')) {
        this.platform = 'windows';
      }
    }

    private init = () => {
        this.__FC.on('mouse:wheel', this._onWheel);
    }
    private unInit = () => {
        this.__FC.off('mouse:wheel', this._onWheel);
    }

    private _lockMouseDown = (
        e: TPointerEventInfo<TPointerEvent> & { alreadySelected?: boolean }
    ) => {
        this._lockPointer = this.__FC.getViewportPoint(e.e as PointerEvent);
    }
    private _lockMouseUp = (_e: TPointerEventInfo<TPointerEvent> & {
        isClick: boolean;
        currentTarget?: FabricObject;
        currentSubTargets: FabricObject[];
    }) => {
        this._lockPointer = null;
    }
    private _lockMouseMove = (event: TPointerEventInfo<PointerEvent>) => {
        if (this._lockPointer) {
            const pointer = this.__FC.getViewportPoint(event.e);
            const delta = new Point(pointer.x - this._lockPointer.x, pointer.y - this._lockPointer.y);
            this.__FC.relativePan(delta);
            this.__FC.requestRenderAll();
            this._lockPointer = pointer;
            const vpt = this.__FC.viewportTransform;
            if (vpt) {
                const left = vpt[4];
                const top = vpt[5];
                this.__FC.fire('viewport:translate', { delta, left, top });
            }
        }
    }

    private _onWheel = (event: TPointerEventInfo<WheelEvent>) => {
        event.e.preventDefault();
        const pointer = this.__FC.getViewportPoint(event.e);
        const normalizedDelta = this.platform === 'mac' ?
                                event.e.deltaY:
                                event.e.deltaY / 4;
        if (event.e.metaKey || event.e.ctrlKey) {
            const delta = normalizedDelta;
            let zoom = this.__FC.getZoom();
            zoom *= 0.99 ** delta;
            zoom = Math.min(Math.max(this.options.minZoom, zoom), this.options.maxZoom);
            this.__FC.zoomToPoint(new Point(pointer.x, pointer.y), zoom);
        } else {
            const delta = new Point(event.e.deltaX, event.e.deltaY);
            const vpt = this.__FC.viewportTransform;
            if (vpt) {
                vpt[4] -= delta.x;
                vpt[5] -= delta.y;
                this.__FC.setViewportTransform(vpt);
                this.__FC.fire('viewport:translate', { delta, left: vpt[4], top: vpt[5] });
            }
        }
        this.__FC.requestRenderAll();
        this.__FC.fire('viewport:zoom', { zoom: this.__FC.getZoom() });
    }
    /**
     * 基于屏幕视口中心缩放
     * @param zoom 缩放比例
     */
    public setZoomByViewPortCenter = (zoom: number) => {
        // 获取视口中心点的屏幕坐标
        const vpt = this.__FC.viewportTransform;
        if (!vpt) return;
        this.__FC.zoomToPoint(this.__FC.getCenterPoint(), zoom);
        this.__FC.fire('viewport:zoom', { zoom: this.__FC.getZoom() });
    }

    public listenLockMove = () => {
        this.__FC.on('mouse:move', this._lockMouseMove);
        this.__FC.on('mouse:down', this._lockMouseDown);
        this.__FC.on('mouse:up', this._lockMouseUp);
    }
    public unListenLockMove = () => {
        this._lockPointer = null;
        this.__FC.off('mouse:move', this._lockMouseMove);
        this.__FC.off('mouse:down', this._lockMouseDown);
        this.__FC.off('mouse:up', this._lockMouseUp);
    }

    public _destroy = () => {
        this.unListenLockMove();
        this.unInit();
    }
}
