import { FabricObject, FixedLayout, Group, GroupProps, LayoutManager } from "fabric";
import { containerDefaultValues } from "./constant";

export class Container extends Group {
    static type = 'Container';
    declare _shape_name_: string;
    static ownDefaults = containerDefaultValues;
    static cacheProperties = Group.cacheProperties;

    static getDefaults(): Record<string, any> {
        return {
            ...super.getDefaults(),
            ...containerDefaultValues,
        };
    }
    constructor(objects: FabricObject[] | Partial<GroupProps>, options: Partial<GroupProps & { _shape_name_: string }>) {
        if (!Array.isArray(objects)) {
            options = objects
            objects = []
        }
        const {flipX, flipY, ...rest} = options
        const opts = { ...Container.ownDefaults, ...rest };
        if (!opts.layoutManager) opts.layoutManager = new LayoutManager(new FixedLayout())
        super(objects, opts);
        this._shape_name_ = opts._shape_name_ || '';
        objects.forEach((child) => {
            child.set({
                _parent_id_: opts._id_,
            });
        });
    }

    toObject(propertiesToInclude: any = []): any {
        return {
            ...super.toObject(propertiesToInclude),
            width: this.width,
            height: this.height,
        };
    }

    toString() {
        return `#<Container: (${this.complexity()})>`;
    }
}