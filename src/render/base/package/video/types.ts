import { IVideoOptionsSchema } from "../../../utils/types"
import { GroupOwnProps, GroupProps } from "fabric";
import { z } from "zod/v4"

export type IVideoOptions = z.infer<typeof IVideoOptionsSchema>

export interface IVideoOwnProps extends GroupOwnProps, Omit<IVideoOptions, 'angle' | 'cover'> {
    clip: boolean;
    cover: HTMLImageElement;
}

export interface IVideoProps extends GroupProps, IVideoOwnProps {}