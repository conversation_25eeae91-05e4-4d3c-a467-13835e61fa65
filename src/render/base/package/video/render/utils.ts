import { util } from "fabric";
import { IVideo } from "../video";

export function drawObjects(this: IVideo, ctx: CanvasRenderingContext2D) {
    for (const obj of this._objects) {
        if (this.canvas?.preserveObjectStacking && obj.group !== this) {
            ctx.save();
            ctx.transform(...util.invertTransform(this.calcTransformMatrix()));
            obj.render(ctx);
            ctx.restore();
        } else if (obj.group === this) {
            obj.render(ctx);
        }
    }
}