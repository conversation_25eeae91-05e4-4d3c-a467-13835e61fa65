export const playIcon = "data:image/svg+xml;base64,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"
export let iconImage: any = undefined
export const createIconImage = (): Promise<HTMLImageElement> => new Promise((resolve, reject) => {
    if (iconImage) return resolve(iconImage);
    iconImage = new Image()
    iconImage.src = playIcon
    iconImage.onload = () => resolve(iconImage);
    iconImage.onerror = () => reject(new Error('Failed to load icon image'));
})