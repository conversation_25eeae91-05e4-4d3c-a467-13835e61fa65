import { LayoutAfterEvent, Rect } from "fabric"
import { IVideo } from "./video"
import { isEqual } from "lodash-es"
import { MaskOptionsGroup } from "../../../utils/types"
import { createMaskContainer } from "../brush/utils"
import { createMask } from "../../../utils/shapes/mask"

export function setClipPath(this: IVideo) {
    const lastClipPath = this.clipPath
    const hasClipPath = this.clip
    const { width, height } = this
    const opts = { width, height }
    if (!hasClipPath && !lastClipPath) return
    if (hasClipPath && lastClipPath) {
        const { width, height } = lastClipPath;
        if (isEqual(opts, { width, height })) return;
        lastClipPath.set({
            ...opts,
            left: 0,
            top: 0,
        });
        lastClipPath.setCoords();
        return;
    }
    const clipPath = hasClipPath ? 
                    new Rect({
                        ...opts,
                        left:0,
                        top: 0,
                        excludeFromExport: true,
                        objectCaching: false,
                    }) : undefined

    this.set('clipPath', clipPath)
}

export function layoutAfter(this: IVideo, _: LayoutAfterEvent) {
    setClipPath.call(this);
}

export function createIVideoMask(this: IVideo, options: MaskOptionsGroup[]) {
       options.forEach(option => {
        const maskContainer = createMaskContainer.call(this, {
            name: option._container_name_,
            opacity: option._opacity_,
        })
        option._mask_options_.forEach(maskOption => {
            createMask.call(maskContainer, maskOption)
        })
        maskContainer.set({
            visible: false,
        })
    })
}