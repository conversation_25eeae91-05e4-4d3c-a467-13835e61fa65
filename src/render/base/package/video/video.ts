import { classRegistry, FabricImage, FabricObject, Group, LayoutManager } from "fabric";
import { videoDefaultValues } from "./constant";
import { IVideoProps } from "./types";
import { layoutAfter } from "./utils";
import { _set } from "./_set";
import { FrameFixedLayout } from "../layout/frameFixed";
import { createIconImage } from "./render/constant";

export class IVideo extends Group {
    static type = 'IVideo';
    declare clip: boolean;
    static ownDefaults = videoDefaultValues;
    static cacheProperties = [...Group.cacheProperties, 'clip'];
    declare sourceSrc: string;
    declare coverSrc: string;
    declare _shape_name_: string;
    declare IConImage: FabricImage;

    static getDefaults(): Record<string, any> {
        return {
            ...super.getDefaults(),
            ...videoDefaultValues,
        };
    }

    constructor(options: Partial<IVideoProps>) {
        const { cover, left, top, scaleX, scaleY, flipX, flipY, ...rest } = options;
        const opts = { ...IVideo.ownDefaults, ...rest };
        opts.layoutManager = opts.layoutManager || new LayoutManager(new FrameFixedLayout());

        if (!options.cover) {
            throw new Error('cover is required');
        }
        if (!options.source) {
            throw new Error('source is required');
        }
        const image = new FabricImage(options.cover, { crossOrigin: 'anonymous' });
        const originalWidth = options.cover.width;
        const originalHeight = options.cover.height;
        const targetWidth = options.width || originalWidth;
        const targetHeight = options.height || originalHeight;

        image.set({
            width: originalWidth,
            height: originalHeight,
            selectable: false,
            dirty: true,
        });


        super([image], opts);
        this.setCoords();
        this.sourceSrc = options.source;
        this.coverSrc = options.cover.src;
        this._shape_name_ = options._shape_name_ || '';
        this.on("layout:after", layoutAfter.bind(this));
        const iconWidth = 44
        const iconHeight = 44
        createIconImage().then(iconImage => {
            iconImage.width = iconWidth
            iconImage.height = iconHeight
            const selfLeft = this.getScaledWidth() / 2 - iconWidth / 2;
            const selfTop = -this.getScaledHeight() / 2 + iconHeight / 2;
            this.IConImage = new FabricImage(iconImage, {
                left: selfLeft,
                top: selfTop,
                width: 44,
                height: 44,
                selectable: false,
                evented: false,
                hasControls: false,
                hasBorders: false,
            });
            this.add(this.IConImage)
            this.IConImage.set({
                left: selfLeft,
                top: selfTop,
            })


            // const updateIconStability = () => {
            //     const canvasZoom = this.canvas?.getZoom() || 1;
            //     const groupWidth = this.getScaledWidth() * canvasZoom
            //     const groupHeight = this.getScaledHeight() * canvasZoom
            //     IConImage.set({
            //         scaleX: 1 / (this.scaleX * canvasZoom),
            //         scaleY: 1 / (this.scaleY * canvasZoom),
            //         left: this.flipX ? -this.width / 2 : this.width / 2,   // 右上角
            //         top: this.flipY ? this.height / 2 : -this.height / 2,
            //         originX: this.flipX ? "left" : "right",
            //         originY: this.flipY ? "bottom" : "top",
            //         flipX: this.flipX,
            //         flipY: this.flipY,
            //         visible: !(
            //             groupWidth < 44 || groupHeight < 44
            //         ),
            //     });
            //     this.canvas?.renderAll()
            // };

            // 绑定事件
            this.on("scaling", this.updateIconStability);
            this.on("rotating", this.updateIconStability);
            this.on("moving", this.updateIconStability);
            this.on("modified", this.updateIconStability);
            this.on("added", this.updateIconStability);
            this.bindEvents()
            // 初始化一次
            this.updateIconStability();
            this.canvas?.renderAll()
        })
        const scale = (options.width && options.height) ? Math.max(options.width / image.width, options.height / image.height) : 1;
        image.scale(scale);
        image.set({ _parent_: this });
        this._setProperties({ width: targetWidth, height: targetHeight, left, top, scaleX, scaleY });
    }
    bindEvents = () => {
        if (this.canvas) {
            this.canvas.on("viewport:translate", this.updateIconStability);
            this.canvas.on("viewport:zoom", this.updateIconStability);
        } else {
            setTimeout(this.bindEvents, 100); // 延迟检查
        }
    };
    _setProperties(properties: Record<string, any>) {
        for (const [key, value] of Object.entries(properties)) {
            this._set(key, value);
        }
    }

    updateIconStability = () => {
        const canvasZoom = this.canvas?.getZoom() || 1;
        const groupWidth = this.getScaledWidth() * canvasZoom
        const groupHeight = this.getScaledHeight() * canvasZoom
        this.IConImage?.set({
            scaleX: 1 / (this.scaleX * canvasZoom),
            scaleY: 1 / (this.scaleY * canvasZoom),
            left: this.flipX ? -this.width / 2 : this.width / 2,   // 右上角
            top: this.flipY ? this.height / 2 : -this.height / 2,
            originX: this.flipX ? "left" : "right",
            originY: this.flipY ? "bottom" : "top",
            flipX: this.flipX,
            flipY: this.flipY,
            visible: !(
                groupWidth < 44 || groupHeight < 44
            ),
        });
        this.canvas?.renderAll()
    };

    remove(...objects: FabricObject[]) {
        this.dirty = false;
        return super.remove(...objects);
    }

    render(ctx: CanvasRenderingContext2D) {
        super.render(ctx)
        // createVideoTag.call(this, ctx)
    }

    set(key: string | Record<string, any>, value?: any) {
        super.set(key, value);
        this.canvas?.fire('object:changed', { target: this });
        return this;
    }

    _set(key: string, value: any) {
        _set.call(this, key, value);
        return this;
    }

    // drawObject(ctx: CanvasRenderingContext2D) {
    //     drawObject.call(this, ctx);
    // }
    // drawObject(ctx: CanvasRenderingContext2D, forClipping: boolean | undefined, context: DrawContext): void {
    //     this._renderBackground(ctx);
    //     drawObjects.call(this, ctx);
    //     createVideoTag.call(this, ctx)
    //     this._drawClipPath(ctx, this.clipPath, context);
    //     super.drawObject(ctx, forClipping, context);
    // }
    toObject(propertiesToInclude: any = []): any {
        return {
            ...super.toObject(propertiesToInclude),
            width: this.width,
            height: this.height,
            clip: this.clip,
        };
    }

    dispose() {
        this.canvas?.off("viewport:translate", this.updateIconStability);
        this.canvas?.off("viewport:zoom", this.updateIconStability);
        super.dispose();
    }

    toString() {
        return `#<IVideo: (${this.complexity()})>`;
    }
}

classRegistry.setClass(IVideo);
