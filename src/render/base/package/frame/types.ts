import { GroupOwnProps, GroupProps } from "fabric";

export interface FrameOwnProps extends GroupOwnProps {
    clip: boolean;
    showLabel: boolean;       // 是否显示标签
    labelText: string;        // 标签文本
    labelFill: string;        // 标签背景填充颜色
    labelTextFill: string;    // 标签文本颜色
    labelFontSize: number;    // 标签字体大小
    labelFontFamily: string;  // 标签字体系列
    _shape_name_: string;
}

export interface FrameProps extends GroupProps, FrameOwnProps {}