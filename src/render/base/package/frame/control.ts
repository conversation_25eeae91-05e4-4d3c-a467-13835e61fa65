import { controlsUtils, TPointerEvent } from 'fabric';

export const customScalingHandler = (
  eventData: TPointerEvent, 
  transform: any,
  x: number, 
  y: number
) => {
  const target = transform.target;
  
  const originalCenter = target.getCenterPoint();
  
  const maxWidth = target.get('maxWidth');
  const maxHeight = target.get('maxHeight');
  const minWidth = target.get('minWidth');
  const minHeight = target.get('minHeight');

  const originalWidth = target.width;
  const originalHeight = target.height;

  const scaled = controlsUtils.scalingEqually(eventData, transform, x, y);
  if (!scaled) return false;

  const newWidth = originalWidth * target.scaleX;
  const newHeight = originalHeight * target.scaleY;

  let finalScaleX = target.scaleX;
  let finalScaleY = target.scaleY;
  let needsAdjustment = false;

  if (newWidth < minWidth || newHeight < minHeight) {
    needsAdjustment = true;
    const scaleXMin = minWidth / originalWidth;
    const scaleYMin = minHeight / originalHeight;
    const scale = Math.max(scaleXMin, scaleYMin);
    finalScaleX = scale;
    finalScaleY = scale;
  }

  if (newWidth > maxWidth || newHeight > maxHeight) {
    needsAdjustment = true;
    const scaleXMax = maxWidth / originalWidth;
    const scaleYMax = maxHeight / originalHeight;
    const scale = Math.min(scaleXMax, scaleYMax);
    finalScaleX = scale;
    finalScaleY = scale;
  }

  if (needsAdjustment) {
    target.set({
      scaleX: finalScaleX,
      scaleY: finalScaleY
    });

    const newCenter = target.getCenterPoint();
    const deltaX = originalCenter.x - newCenter.x;
    const deltaY = originalCenter.y - newCenter.y;

    target.set({
      left: target.left + deltaX,
      top: target.top + deltaY
    });

    target.setCoords();
    return false;
  }

  return true;
};
