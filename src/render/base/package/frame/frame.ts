import {Abortable, classRegistry, FabricObject, Group, LayoutManager, Point, util} from "fabric";
import {FrameFixedLayout} from "../layout/frameFixed";
import {frameDefaultValues} from './constants'
import {FrameProps} from "./types";
import {drawObject, layoutAfter} from "./util";
import {getLabelBoundsByCanvas, renderLabel} from "../utils/renderLabel";
import {FramePreset} from "../preset";

export class Frame extends Group {
    static type = 'Frame'
    declare clip: boolean
    declare showLabel: boolean;
    declare labelText: string;
    declare labelFill: string;
    declare labelTextFill: string;
    declare labelFontSize: number;
    declare labelFontFamily: string;
    declare _shape_name_: string;
    declare _label: any;
    static ownDefaults = frameDefaultValues
    static cacheProperties = [...Group.cacheProperties, 'clip']
    static getDefaults(): Record<string, any> {
        return {
            ...super.getDefaults(),
            ...Frame.ownDefaults,
        }
    }
    constructor(objects: FabricObject[] | Partial<FrameProps>, options: Partial<FrameProps> = {}) {
        if (!Array.isArray(objects)) {
            objects = []
        }
        const opts = {...Frame.ownDefaults, ...options}
        if (!opts.layoutManager) opts.layoutManager = new LayoutManager(new FrameFixedLayout())
        super(objects, opts);
        this._shape_name_ = opts._shape_name_ || '';
        this.set('labelText', options.labelText)
        this.set('maxWidth', FramePreset.maxWidth)
        this.set('maxHeight', FramePreset.maxHeight)
        this.set('minWidth', FramePreset.minWidth)
        this.set('minHeight', FramePreset.minHeight)
        this.on("layout:after", layoutAfter.bind(this))
    }

    _set(key: string, value: any) {
        return super._set(key, value)
    }

    render(ctx: CanvasRenderingContext2D) {
        super.render(ctx);
        if (this.showLabel && this.labelText) {
            renderLabel.call(this, ctx);
        }
    }

    getLabelBounds() {
        if (!this.canvas) return;
        return getLabelBoundsByCanvas.call(this);
    }

    isPointInLabel(point: Point) {
        const labelBounds = this.getLabelBounds();
        if (!labelBounds) return false;
        const { left, top, width, height } = labelBounds;
        return point.x >= left && point.x <= left + width &&
            point.y >= top && point.y <= top + height;
    }

    remove(...objects: FabricObject[]) {
        this.dirty = false
        return super.remove(...objects)
    }

    drawObject(ctx: CanvasRenderingContext2D) {
        drawObject.call(this, ctx)
    }
    set(key: string | Record<string, any>, value?: any) {
        if (key === 'scaleX' || key === 'scaleY') {
            const maxWidth = this.get('maxWidth') || 0
            const maxHeight = this.get('maxHeight') || 0
            const minWidth = this.get('minWidth') || 0
            const minHeight = this.get('minHeight') || 0
            if (maxWidth === 0 || maxHeight === 0 || minWidth === 0 || minHeight === 0) {
                super.set(key, value)
                this.canvas?.fire('object:changed', { target: this })
                return this
            }
            const maxScaleX = maxWidth / this.width
            const maxScaleY = maxHeight / this.height
            const minScaleX = minWidth / this.width
            const minScaleY = minHeight / this.height
            
            const maxScale = Math.min(maxScaleX, maxScaleY)
            const minScale = Math.max(minScaleX, minScaleY)
            
            value = Math.min(Math.max(value, minScale), maxScale)
            
            super.set('scaleX', value)
            super.set('scaleY', value)
            this.setCoords()
            this.canvas?.fire('object:changed', { target: this })
            return this
        }
        super.set(key, value)
        this.canvas?.fire('object:changed', { target: this })
        return this
    }
    
    toObject(propertiesToInclude: any = []):any {
        return {
            ...super.toObject(propertiesToInclude),
            width: this.width,
            height: this.height,
            clip: this.clip,
        };
    }
    toString() {
        return `#<Frame: (${this.complexity()})>`;
    }
    static async fromObject({ type: _, objects = [], layoutManager, ...options }: any, abortable?: Abortable) {
        try {
            const [list, hydratedOptions] = await Promise.all([
                util.enlivenObjects<FabricObject>(objects, abortable),
                util.enlivenObjectEnlivables(options, abortable),
            ]);
            let layoutM;
            if (layoutManager) {
                const layoutClass = classRegistry.getClass<typeof LayoutManager>(layoutManager.type);
                const strategy = layoutManager.strategy;
                const strategyClass = classRegistry.getClass<typeof FrameFixedLayout>(strategy);
                layoutM = new layoutClass(new strategyClass());
            }

            return new this(list, {
                ...options,
                ...hydratedOptions,
                layoutManager: layoutM,
            });
        } catch (e) {
            return Promise.reject(e);
        }
    }
}

classRegistry.setClass(Frame);