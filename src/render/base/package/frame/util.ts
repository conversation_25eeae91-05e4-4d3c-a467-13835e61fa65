import { LayoutAfterEvent, Rect, TSimplePathData, util } from "fabric";
import { Frame } from './frame'
import { isEqual } from "lodash-es";
import { IImage } from "../image/image";

export function setClipPath(this: Frame | IImage) {
    const lastClipPath = this.clipPath
    const hasClipPath = this.clip
    const { width, height } = this
    const opts = { width, height }
    if (!hasClipPath && !lastClipPath) return
    if (hasClipPath && lastClipPath) {
        const { width, height } = lastClipPath;
        if (isEqual(opts, { width, height })) return;
        lastClipPath.set({
            ...opts,
            left: 0,
            top: 0,
        });
        lastClipPath.setCoords();
        return;
    }
    const clipPath = hasClipPath ? 
                    new Rect({
                        ...opts,
                        left:0,
                        top: 0,
                        excludeFromExport: true,
                        objectCaching: false,
                    }) : undefined

    this.set('clipPath', clipPath)
}

/**
 * 设置最小尺寸
 * @param this 
 * @param key 
 * @param value 
 * @returns 
 */
export function setMinSize(this: Frame, key: string, value: number) {
    if (key === 'scaleX' || key === 'scaleY') {
        const minWidth = this.get('minWidth')
        const minHeight = this.get('minHeight')
        const width = this.width * (key === 'scaleX' ? value : this.scaleX)
        const height = this.height * (key === 'scaleY' ? value : this.scaleY)
        if (width < minWidth || height < minHeight) {
            const scaleX = width < minWidth ? minWidth / this.width : value
            const scaleY = height < minHeight ? minHeight / this.height : value
            const scale = Math.max(scaleX, scaleY)
            if (key === 'scaleX') {
                value = scale
            } else {
                value = scale
            }
            this.setCoords()
        }
    }
    return { key, value }
}

/**
 * 设置最大尺寸
 * @param this 
 * @param key 
 * @param value 
 * @returns 
 */
export function setMaxSize(this: Frame, key: string, value: number) {
    if (key === 'scaleX' || key === 'scaleY') {
        const maxWidth = this.get('maxWidth')
        const maxHeight = this.get('maxHeight')
        const width = this.width * (key === 'scaleX' ? value : this.scaleX)
        const height = this.height * (key === 'scaleY' ? value : this.scaleY)
        
        if (width > maxWidth || height > maxHeight) {
            const scaleX = width > maxWidth ? maxWidth / this.width : value
            const scaleY = height > maxHeight ? maxHeight / this.height : value
            const scale = Math.min(scaleX, scaleY)
            if (key === 'scaleX') {
                value = scale
            } else {
                value = scale
            }
        }
    }
    
    return setMinSize.call(this, key, value)
}



export function layoutAfter(this: Frame, _: LayoutAfterEvent) {
    setClipPath.call(this);
}

export function drawObject(this: Frame, ctx: CanvasRenderingContext2D) {
    this._renderBackground(ctx);
    renderFill.call(this, ctx);
    if (this.clipPath) {
        ctx.save();
        this._removeShadow(ctx);
        this.clipPath.fill = "transparent";
        this.clipPath.render(ctx);
        ctx.clip();
        drawObjects.call(this, ctx);
        ctx.restore();
        renderBorder.call(this, ctx);
    } else {
        renderBorder.call(this, ctx);
        ctx.save();
        this._removeShadow(ctx);
        drawObjects.call(this, ctx);
        ctx.restore();
    }

}

function drawObjects(this: Frame, ctx: CanvasRenderingContext2D) {
    for (const obj of this._objects) {
        if (this.canvas?.preserveObjectStacking && obj.group !== this) {
            ctx.save();
            ctx.transform(...util.invertTransform(this.calcTransformMatrix()));
            obj.render(ctx);
            ctx.restore();
        } else if (obj.group === this) {
            obj.render(ctx);
        }
    }
}

function renderBorder(this: Frame, ctx: CanvasRenderingContext2D) {
    if (!this.hasStroke()) return;
    const lastFill = this.fill;
    this.fill = null;
    Rect.prototype._render.call(this, ctx);
    this.fill = lastFill;
}

function renderFill(this: Frame, ctx: CanvasRenderingContext2D) {
    if (!this.fill) return;
    const lastStroke = this.stroke;
    this.stroke = null;
    Rect.prototype._render.call(this, ctx);
    this.stroke = lastStroke;
}


/**
 * A wrapper around Number#toFixed, which contrary to native method returns number, not string.
 * @param {number|string} number number to operate on
 * @param {number} fractionDigits number of fraction digits to "leave"
 * @return {number}
 */
export const toFixed = (number: number | string, fractionDigits: number) =>
    parseFloat(Number(number).toFixed(fractionDigits));

/**
 * Join path commands to go back to svg format
 * @param {TSimplePathData} pathData fabricJS parsed path commands
 * @param {number} fractionDigits number of fraction digits to "leave"
 * @return {String} joined path 'M 0 0 L 20 30'
 */
export const joinPath = (pathData: TSimplePathData, fractionDigits?: number) =>
    pathData
      .map((segment) => {
        return segment
          .map((arg, i) => {
            if (i === 0) return arg;
            return fractionDigits === undefined
              ? arg
              : toFixed(arg, fractionDigits);
          })
          .join(' ');
      })
      .join(' ');