import { classRegistry, FabricImage, FabricObject, Group, LayoutManager } from "fabric";
import { imageDefaultValues } from "./constant";
import { IImageProps } from "./types";
import { createIImageMask, layoutAfter } from "./utils";
import { _set } from "./_set";
import { FrameFixedLayout } from "../layout/frameFixed";

export class IImage extends Group {
    static type = 'IImage';
    declare clip: boolean;
    declare showInfo: boolean;
    declare _shape_name_: string;
    static ownDefaults = imageDefaultValues;
    static cacheProperties = [...Group.cacheProperties, 'clip'];

    static getDefaults(): Record<string, any> {
        return {
            ...super.getDefaults(),
            ...imageDefaultValues,
        };
    }

    constructor(options: Partial<IImageProps> & { src: string }) {
        const { src, left, top, scaleX, scaleY, flipX, flipY, ...rest } = options;
        const opts = { ...IImage.ownDefaults, ...rest };
        opts.layoutManager = opts.layoutManager || new LayoutManager(new FrameFixedLayout());

        if (!options.image) {
            throw new Error('image is required');
        }

        const image = new FabricImage(options.image, { crossOrigin: 'anonymous' });
        const originalWidth = options.image.width;
        const originalHeight = options.image.height;
        const targetWidth = options.width || originalWidth;
        const targetHeight = options.height || originalHeight;

        image.set({
            width: originalWidth,
            height: originalHeight,
            selectable: false,
            dirty: true,
        });

        super([image], opts);
        this.setCoords();
        this._shape_name_ = options._shape_name_ || '';
        this.on("layout:after", layoutAfter.bind(this));

        const scale = (options.width && options.height) ? Math.max(options.width / image.width, options.height / image.height) : 1;
        image.scale(scale);
        image.set({ _parent_: this });
        this._setProperties({ width: targetWidth, height: targetHeight, left, top, scaleX, scaleY});
        createIImageMask.call(this, options._mask_options_ || [])
    }

    _setProperties(properties: Record<string, any>) {
        for (const [key, value] of Object.entries(properties)) {
            this._set(key, value);
        }
    }

    remove(...objects: FabricObject[]) {
        this.dirty = false;
        return super.remove(...objects);
    }

    set(key: string | Record<string, any>, value?: any) {
        super.set(key, value);
        this.canvas?.fire('object:changed', { target: this });
        return this;
    }

    _set(key: string, value: any) {
        _set.call(this, key, value);
        return this;
    }
    // render(ctx: CanvasRenderingContext2D) {
    //     super.render(ctx);
    //     const canvas = this.canvas;
    //     if (!canvas) return;
    //     if (this.showInfo) {
    //         renderImage.call(this, ctx);
    //     }
    // }

    // drawObject(ctx: CanvasRenderingContext2D) {
    //     drawObject.call(this, ctx);
    // }

    toObject(propertiesToInclude: any = []): any {
        return {
            ...super.toObject(propertiesToInclude),
            width: this.width,
            height: this.height,
            clip: this.clip,
        };
    }

    toString() {
        return `#<Frame: (${this.complexity()})>`;
    }
}

classRegistry.setClass(IImage);