import { LayoutAfterEvent, Rect } from "fabric"
import { IImage } from "./image"
import { isEqual } from "lodash-es"
import { MaskOptionsGroup } from "../../../utils/types"
import { createMaskContainer } from "../brush/utils"
import { createMask } from "../../../utils/shapes/mask"
import { getFontWidthAndHeight } from "../utils/canvasFontCalc"

export function setClipPath(this: IImage) {
    const lastClipPath = this.clipPath
    const hasClipPath = this.clip
    const { width, height } = this
    const opts = { width, height }
    if (!hasClipPath && !lastClipPath) return
    if (hasClipPath && lastClipPath) {
        const { width, height } = lastClipPath;
        if (isEqual(opts, { width, height })) return;
        lastClipPath.set({
            ...opts,
            left: 0,
            top: 0,
        });
        lastClipPath.setCoords();
        return;
    }
    const clipPath = hasClipPath ? 
                    new Rect({
                        ...opts,
                        left:0,
                        top: 0,
                        excludeFromExport: true,
                        objectCaching: false,
                    }) : undefined

    this.set('clipPath', clipPath)
}

export function layoutAfter(this: IImage, _: LayoutAfterEvent) {
    setClipPath.call(this);
}

export function createIImageMask(this: IImage, options: MaskOptionsGroup[]) {
       options.forEach(option => {
        const maskContainer = createMaskContainer.call(this, {
            name: option._container_name_,
            opacity: option._opacity_,
        })
        option._mask_options_.forEach(maskOption => {
            createMask.call(maskContainer, maskOption)
        })
        maskContainer.set({
            visible: false,
        })
    })
}

export function renderImage(this: IImage, ctx: CanvasRenderingContext2D) {
    if (!this.canvas) return;
    const activeObject = this.canvas.getActiveObject();
    if (activeObject !== this) return;
    ctx.save();
    const offsetY = 2;
    const paddingX = 4;
    const paddingY = 2;
    const width = this.getScaledWidth();
    const height = this.getScaledHeight();
    const labelX = this.getCenterPoint().x;
    const labelY = this.getCenterPoint().y + height / 2 + offsetY;
    const { width: fontWidth, height: fontHeight } = getFontWidthAndHeight(`${Math.floor(width)} * ${Math.floor(height)}`, 12, 'Arial');
    ctx.fillStyle = '#1D1E23';
    ctx.beginPath();
    ctx.strokeStyle = '#22272E';

    const round = 4 / this.canvas.getZoom()
    const rectWidth = (fontWidth + paddingX * 2) / this.canvas.getZoom();
    const rectHeight = (fontHeight + paddingY) / this.canvas.getZoom();
    const rectX = labelX - rectWidth / 2;
    const rectY = labelY + rectHeight / 2;
    if (ctx.roundRect) {
        ctx.roundRect(rectX, rectY, rectWidth, rectHeight, round);
    } else {
        ctx.rect(rectX, rectY, rectWidth, rectHeight);
    }
    ctx.fill();
    ctx.stroke();

    ctx.fillStyle = '#FFFFFF';
    ctx.font = `${12 / this.canvas.getZoom()}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(`${Math.floor(width)} * ${Math.floor(height)}`, rectX + rectWidth / 2, rectY + rectHeight / 2);
    ctx.restore();
}