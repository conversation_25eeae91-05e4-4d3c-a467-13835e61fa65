import { Rect } from 'fabric';

export class Animate {
    private background: Rect;
    private animations: any[] = [];
    private animationFrame: number | null = null;
    private startTime: number = 0;
    private speed: number;

    constructor(background: Rect, speed: number = 1) {
        this.background = background;
        this.speed = speed;
    }

    start() {
        this.stop();
        this.startTime = Date.now();
        this.animate();
    }

    setSpeed(speed: number) {
        this.speed = speed;
    }

    private animate = () => {
        const currentTime = Date.now();
        const elapsed = (currentTime - this.startTime) / 1000;

        const overlayOpacity = 0.5 + Math.sin(elapsed * this.speed) * 0.15;
        this.background.set('opacity', overlayOpacity);

        this.animationFrame = requestAnimationFrame(this.animate);
        
        if (this.background?.canvas) {
            this.background.canvas.requestRenderAll();
        }
    };

    stop() {
        if (this.animationFrame !== null) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }

        this.background.set('opacity', 0.5);

        this.animations.forEach(animation => {
            if (animation && typeof animation.abort === 'function') {
                animation.abort();
            }
        });
        this.animations = [];
    }
}