import { FabricImage, Group, IText, Rect, classRegistry, filters } from 'fabric';
import { defaultLoaderValues } from './constant';
import { LoaderProps } from './types';
import { Animate } from './animate';
import { merge } from 'lodash-es';
import { IImage } from '../../image/image';

export class Loader extends Group {
    static type = 'loader';
    static ownDefaults = defaultLoaderValues;
    static cacheProperties = Group.cacheProperties;
    private _loaderAnimate: Animate | null = null;
    private _bottomLeftText: IText
    private background: Rect | null = null;
    private _images: FabricImage[] = [];

    static getDefaults(): Record<string, any> {
        return {
            ...super.getDefaults(),
            ...Loader.ownDefaults,
        };
    }

    constructor(options: Partial<LoaderProps> & {el: IImage}) {
        const opts = merge(Loader.ownDefaults, options);
        super([], opts);
        this._bottomLeftText = new IText(opts.el._loading_text_ || opts.loadingText, {
            fontSize: opts.fontSize,
            fill: opts.fill,
        });
        this.createBackground();
        this.createBottomLeftText();
    }

    private createBackground() {
        if (this.background) {
            this.remove(this.background);
        }
        this.background = new Rect({
            width: this.getScaledWidth() || 200,
            height: this.getScaledHeight() || 200,
            fill: 'black',
            opacity: 0.5,
            selectable: false,
            evented: false
        });
        this.add(this.background);
    }


    private createBottomLeftText() {
        this.add(this._bottomLeftText);

        this._bottomLeftText.set({
            left: -this.width / 2 + this._bottomLeftText.width / 2 + 10,
            top: this.height / 2 - this._bottomLeftText.height,
        });
    }

    public resetBlurValue = () => {
        this._images.forEach(item => {
            item.filters = item.filters.filter(filter => !(filter instanceof filters.Blur))
            item.applyFilters()
        })
        this.canvas?.requestRenderAll()
    }
    
    setSpeed(speed: number) {
        if (this._loaderAnimate) {
            this._loaderAnimate.setSpeed(speed);
        }
    }

    initialize(speed: number = 2) {
        if (this._loaderAnimate) {
            this._loaderAnimate.stop();
        }
        
        if (this.background) {
            this._loaderAnimate = new Animate(this.background, speed);
            this._loaderAnimate.start();
        }

        this.canvas?.requestRenderAll();
    }

    dispose() {
        if (this._loaderAnimate) {
            this._loaderAnimate.stop();
            this._loaderAnimate = null;
        }
        super.dispose();
    }
}

classRegistry.setClass(Loader);