import { Circle } from 'fabric';

export class Animate {
    private circles: Circle[];
    private animations: any[] = [];
    private animationFrame: number | null = null;
    private startTime: number = 0;
    private speed: number;
    private maxCircleEdge: number;
    private minCircleEdge: number;

    constructor(circles: Circle[], speed: number = 1, maxCircleEdge: number, minCircleEdge: number) {
        this.circles = circles;
        this.speed = speed;
        this.maxCircleEdge = maxCircleEdge;
        this.minCircleEdge = minCircleEdge;
    }

    start() {
        this.stop();
        this.startTime = Date.now();
        this.animate();
    }

    setSpeed(speed: number) {
        this.speed = speed;
    }

    private animate = () => {
        const currentTime = Date.now();
        const elapsed = (currentTime - this.startTime) / 1000;
        // 设置圆的缩放值，轮流缩放
        this.circles.forEach((circle, index) => {
            // 每个圆之间的时间差为 PI/2，确保前一个放大完成后下一个开始放大
            const scale = this.minCircleEdge + (this.maxCircleEdge - this.minCircleEdge) * (1 + Math.sin(elapsed * this.speed + index * Math.PI/2)) / 2;
            circle.set({
                radius: scale,
            });
        });
        this.animationFrame = requestAnimationFrame(this.animate);
        if (this.circles[0]?.canvas) {
            this.circles[0].canvas.requestRenderAll();
        }
    };

    stop() {
        if (this.animationFrame !== null) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }

        this.circles.forEach(circle => {
            circle.set({
                scaleX: 1,
                scaleY: 1,
            });
        });

        this.animations.forEach(animation => {
            if (animation && typeof animation.abort === 'function') {
                animation.abort();
            }
        });
        this.animations = [];
    }
}