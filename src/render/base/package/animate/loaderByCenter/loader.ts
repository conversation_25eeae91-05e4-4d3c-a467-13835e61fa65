import { Circle, FabricImage, Group, IText, Rect, classRegistry, filters } from 'fabric';
import { defaultLoaderValues } from './constant';
import { LoaderProps } from './types';
import { Animate } from './animate';
import { merge } from 'lodash-es';
import { IImage } from '../../image/image';
import { getLoaderByCenterSizeByEdge } from '../../preset/loading/loaderByCenter';

export class CircleDanceLoader extends Group {
    static type = 'loader';
    static ownDefaults = defaultLoaderValues;
    static cacheProperties = Group.cacheProperties;
    private _loaderAnimate: Animate | null = null;
    private _centerText: IText
    private background: Rect | null = null;
    private filter: filters.BaseFilter<string, Record<string, any>, Record<string, any>>[] = [];
    private ImageTarget: FabricImage | null = null;
    private circles: Circle[] = [];
    private calcSize: {
        maxCircleEdge: number;
        minCircleEdge: number;
        circleGap: number;
        fontSize: number;
        circleTop: number;
        fontTop: number;
        fontLeft: number;
    }

    static getDefaults(): Record<string, any> {
        return {
            ...super.getDefaults(),
            ...CircleDanceLoader.ownDefaults,
        };
    }

    constructor(options: Partial<LoaderProps> & {el: IImage}) {
        const opts = merge(CircleDanceLoader.ownDefaults, options);
        super([], opts);
        const calcSize = getLoaderByCenterSizeByEdge(this.getScaledWidth() || 200, this.getScaledHeight() || 200);
        this._centerText = new IText(opts.el._loading_text_ || opts.loadingText, {
            fontSize: calcSize.fontSize,
            fill: opts.fill,
        });
        this.calcSize = calcSize;
        this.createBackground(opts.maskOpacity);
        this.createCenterLeftText();
        this.createCircle();
        this.setBlur(opts.blur, opts.el);
    }
    private createBackground(maskOpacity: number) {
        if (this.background) {
            this.remove(this.background);
        }
        this.background = new Rect({
            width: this.getScaledWidth() || 200,
            height: this.getScaledHeight() || 200,
            fill: 'black',
            opacity: maskOpacity,
            selectable: false,
            evented: false
        });
        this.add(this.background);
    }

    private createCircle() {
        this.circles = new Array(3).fill(0).map(() => {
            const circle = new Circle({
                radius: this.calcSize.maxCircleEdge,
                fill: 'white',
                opacity: 0.5,
            });
            return circle;
        });
        this.circles.forEach((item, index) => {
            item.set({
                left: (index - 1) * this.calcSize.circleGap,
                top: 0,
            });
            this.add(item);
        });
    }


    private createCenterLeftText() {
        this.add(this._centerText);
        this._centerText.set({
            left: this.calcSize.fontLeft,
            top: this.calcSize.fontTop,
        });
    }

    setBlur(blur: boolean, element: IImage) {
        if (!blur) return;
        const filter = new filters.Blur({ blur:1 });
        this.filter.push(filter);
        this.ImageTarget = element.getObjects().find(item => item.type === 'image') as FabricImage;
        if (this.ImageTarget) {
            this.ImageTarget.filters.push(...this.filter);
            this.ImageTarget.applyFilters();
            if (this.ImageTarget.canvas) {
                this.ImageTarget.canvas.renderAll();
            }
        }
    }

    
    setSpeed(speed: number) {
        if (this._loaderAnimate) {
            this._loaderAnimate.setSpeed(speed);
        }
    }

    initialize(speed: number = 2) {
        if (this._loaderAnimate) {
            this._loaderAnimate.stop();
        }
        
        if (this.circles.length > 0) {
            this._loaderAnimate = new Animate(
                this.circles,
                speed,
                this.calcSize.maxCircleEdge,
                this.calcSize.minCircleEdge
            );
            this._loaderAnimate.start();
        }

        

        this.canvas?.requestRenderAll();
    }

    dispose() {
        if (this._loaderAnimate) {
            this._loaderAnimate.stop();
            this._loaderAnimate = null;
        }
        if (this.ImageTarget) {
            this.ImageTarget.filters = this.ImageTarget.filters.filter(item => !this.filter.includes(item));
            this.ImageTarget.applyFilters();
            if (this.ImageTarget.canvas) {
                this.ImageTarget.canvas.renderAll();
            }
            this.filter = [];
        }
        super.dispose();
    }
}

classRegistry.setClass(CircleDanceLoader);