import { Rect } from "fabric";

export const defaultErrorValues = {
    ...Rect.ownDefaults,
    subTargetCheck: false,
    interactive: false,
    clip: false,
    selectable: false,
    hasControls: false,
}

export const defaultOptions = {
    originX: 'center',
    originY: 'center',
    stroke: 'rgba(0, 0, 0, 0.1)',
    strokeWidth: 1,
    strokeUniform: true,
    hasControls: false,
}
export const errorSvg = `
    <svg width="65" height="64" viewBox="0 0 65 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="exclamationmarkTriangle">
<g id="Vector">
<path d="M32.5008 19.3333C33.6054 19.3333 34.5008 20.2288 34.5008 21.3333V34.6667C34.5008 35.7712 33.6054 36.6667 32.5008 36.6667C31.3963 36.6667 30.5008 35.7712 30.5008 34.6667V21.3333C30.5008 20.2288 31.3963 19.3333 32.5008 19.3333Z" fill="white" style="mix-blend-mode:soft-light"/>
<path d="M32.5008 47.3333C34.1577 47.3333 35.5008 45.9902 35.5008 44.3333C35.5008 42.6765 34.1577 41.3333 32.5008 41.3333C30.844 41.3333 29.5008 42.6765 29.5008 44.3333C29.5008 45.9902 30.844 47.3333 32.5008 47.3333Z" fill="white" style="mix-blend-mode:soft-light"/>
<path d="M28.0143 7.8877C30.0671 4.33214 35.1991 4.33214 37.252 7.8877L60.6883 48.4807C62.7411 52.0363 60.1751 56.4807 56.0695 56.4807H9.19676C5.09116 56.4807 2.52515 52.0363 4.57796 48.4807L28.0143 7.8877ZM33.7878 9.8877C33.2746 8.99881 31.9916 8.99881 31.4784 9.8877L8.04206 50.4807C7.52886 51.3696 8.17036 52.4807 9.19676 52.4807H56.0695C57.0959 52.4807 57.7374 51.3696 57.2242 50.4807L33.7878 9.8877Z" fill="white" style="mix-blend-mode:soft-light"/>
</g>
</g>
</svg>
`

