import { Point, IText, util } from "fabric";
import { Frame } from "../frame/frame";

const paddingX = 8
const paddingY = 2
const labelGap = 8

export function getFontWidthAndHeight(this: Frame) {
    const text = new IText(this.labelText, {
        fontSize: this.labelFontSize,
        fontFamily: this.labelFontFamily,
    })
    text.initDimensions()
    return {
        width: (text.width + paddingX * 2),
        height: (text.height + paddingY * 2),
    }
}

export function getLabelBounds(this: Frame) {
    const centerPointer = this.getCenterPoint()
    const top = centerPointer.y - (this.getScaledHeight() / 2)
    const left = centerPointer.x - (this.getScaledWidth() / 2)
    const { width, height } = getFontWidthAndHeight.call(this)
    return {
        top,
        left,
        width,
        height
    }
}

export function getLabelBoundsByCanvas(this: Frame) {
    if (!this.canvas) return;
    const { left, top, height, width } = getLabelBounds.call(this);
    const startPoint = new Point(left, top);
    const endPoint = new Point(left + width, top + height);
    const viewPort = this.canvas.viewportTransform;
    const targetMatrix = util.invertTransform(viewPort)
    const transformedStart = startPoint.transform(targetMatrix);
    const transformedEnd = endPoint.transform(targetMatrix);
    const fixWidth = transformedEnd.x - transformedStart.x
    const fixHeight = transformedEnd.y - transformedStart.y
    const gap = labelGap
    return {
        left,
        top: top - fixHeight - gap,
        width: fixWidth,
        height: fixHeight
    }
}

export function renderLabel(this: Frame, ctx: CanvasRenderingContext2D) {
    if (!this.canvas) return;
    ctx.save();

    const { left, top, height, width } = getLabelBounds.call(this);
    const startPoint = new Point(left, top);
    const endPoint = new Point(left + width, top + height);
    const viewPort = this.canvas.viewportTransform;
    const targetMatrix = util.invertTransform(viewPort)
    const transformedStart = startPoint.transform(targetMatrix);
    const transformedEnd = endPoint.transform(targetMatrix);
    const fixWidth = transformedEnd.x - transformedStart.x
    const fixHeight = transformedEnd.y - transformedStart.y


    ctx.fillStyle = '#1D1E23';
    ctx.beginPath();
    ctx.strokeStyle = '#22272E';
    const gap = labelGap
    const round = 4 / this.canvas.getZoom()
    if (ctx.roundRect) {
        ctx.roundRect(left, top - fixHeight - gap, fixWidth, fixHeight, round);
    } else {
        ctx.rect(left, top - fixHeight - gap, fixWidth, fixHeight);
    }
    ctx.fill();
    ctx.stroke();

    ctx.fillStyle = this.labelTextFill || 'white';
    ctx.font = `${(this.labelFontSize || 14) / this.canvas.getZoom()}px ${this.labelFontFamily || 'Arial'}`;
    ctx.textAlign = 'start';
    ctx.textBaseline = 'middle';
    ctx.fillText(
        this.labelText,
        left + (paddingX / this.canvas.getZoom()),
        top - (fixHeight) + (paddingY / this.canvas.getZoom())
    );

    ctx.restore();
}