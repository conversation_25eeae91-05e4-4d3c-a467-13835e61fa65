import { classReg<PERSON>ry, Pattern, Rect, RectProps } from "fabric";
import { smartMaskDefaultValues } from "./constants";
import { ElementName } from "../../../utils/enum";
import { nanoid } from 'nanoid'
export class SmartMask extends Rect {
    static type = 'SmartMask';
    static ownDefaults = smartMaskDefaultValues;
    static cacheProperties = [...Rect.cacheProperties, 'image']
    static getDefaults(): Record<string, any> {
        return {
            ...super.getDefaults(),
            ...smartMaskDefaultValues,
        };
    }
    image: HTMLImageElement;
    revertImage: HTMLImageElement;
    constructor(options: Partial<RectProps> & {
         img: HTMLImageElement,
         revertImage: HTMLImageElement,
        }) {
        const pattern = new Pattern({
            source: options.img,
            repeat: 'no-repeat',
        });
        const opts = { ...SmartMask.ownDefaults, ...options, fill: pattern };
        super(opts);
        this.image = opts.img;
        this.revertImage = opts.revertImage;
        this.setCoords();
        this.set('_name_', ElementName.MASK_SMART);
        this.set('_id_', nanoid())
        this.set('angle', opts.angle)
    }

    replacePattern = () => {
        const pattern = new Pattern({
            source: this.revertImage,
            repeat: 'no-repeat',
        });
        super.set("fill", pattern)
        super.setCoords()
        this.canvas?.renderAll()
    }

    resetPattern = () => {
        const pattern = new Pattern({
            source: this.image,
            repeat: 'no-repeat',
        });
        super.set("fill", pattern)
        super.setCoords()
        this.canvas?.renderAll()
    }
}


classRegistry.setClass(SmartMask)