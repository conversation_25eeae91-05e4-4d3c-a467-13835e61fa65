
export function transformImageData(imageData: ImageData) {
    const offCanvas = document.createElement('canvas');
    const offCtx = offCanvas.getContext('2d');
    if (!offCtx) throw new Error('ctx is null');
    offCanvas.width = imageData.width;
    offCanvas.height = imageData.height;
    offCtx.putImageData(imageData, 0, 0);
    const dataUrl = offCanvas.toDataURL('image/png');
    const image = new Image();
    image.src = dataUrl;
    return image;
}