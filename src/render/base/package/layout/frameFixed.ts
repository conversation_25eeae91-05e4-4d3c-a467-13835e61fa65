import { InitializationLayoutContext, LayoutStrategy, LayoutStrategyResult, Point, StrictLayoutContext, classRegistry, FabricObject } from 'fabric'
import { LAYOUT_TYPE_IMPERATIVE } from '../frame/constants'
export class FrameFixedLayout extends LayoutStrategy {
    static readonly type = 'FrameFixedLayout'

    getInitialSize (
        { target }: StrictLayoutContext & InitializationLayoutContext,
        { size } : Pick<LayoutStrategyResult, "center" | "size">
    ) {
        return new Point(target.width || size.x, target.height || size.y)
    }

    shouldPerformLayout(): boolean {
        return false
    }

    calcBoundingBox(_: FabricObject[], context: StrictLayoutContext): LayoutStrategyResult | undefined {
        if (context.type === LAYOUT_TYPE_IMPERATIVE && context.overrides) return context.overrides
        const { width, height } = context.target
        const center = context.target.getRelativeCenterPoint()
        const size = new Point(width || 0, height || 0)
        return {
            center,
            size
        }
        
    }
}

classRegistry.setClass(FrameFixedLayout)