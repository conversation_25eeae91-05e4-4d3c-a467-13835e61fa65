import { ErrorPresetType } from "./type";

import { merge } from "lodash-es";
export const ErrorPreset: ErrorPresetType = {
    fontSize: 14,
    baseEdge: 460,
    fontTop: 36,
    fontLeft: 0,

    iconLeft: 0,
    iconTop: -4,
}

export const setErrorPreset = (preset: ErrorPresetType) => {
    merge(ErrorPreset, preset)
}

export const getErrorPreset = (width: number, height: number) => {
    const widthRatio = width / ErrorPreset.baseEdge
    const heightRatio = height / ErrorPreset.baseEdge
    return {
        fontSize: ErrorPreset.fontSize * widthRatio,
        fontTop: ErrorPreset.fontTop * heightRatio,
        fontLeft: ErrorPreset.fontLeft * widthRatio,

        iconLeft: ErrorPreset.iconLeft * widthRatio,
        iconTop: ErrorPreset.iconTop * heightRatio,
    }
}


