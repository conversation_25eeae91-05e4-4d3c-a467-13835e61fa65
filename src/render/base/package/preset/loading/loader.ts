/**
 * 默认loading使用等比预设
 */
import { merge } from "lodash-es";
import { LoaderPresetType } from "./type";

export const LoaderPreset: LoaderPresetType = {
    fontSize: 14, // 字体大小
    baseEdge: 46, // 基础边长
}


export const setLoaderPreset = (preset: LoaderPresetType) => {
    merge(LoaderPreset, preset)
}

/**
 * 获取字体大小
 * @param edge 
 * @returns 
 */
export const getLoadingFontSize = (edge: number) => {
    return edge * (LoaderPreset.fontSize / LoaderPreset.baseEdge)
}