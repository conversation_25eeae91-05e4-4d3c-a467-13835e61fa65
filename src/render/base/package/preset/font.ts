import { ITextProps } from 'fabric'
import { merge} from 'lodash-es'
export const DefaultFontPreset: Partial<ITextProps> = {
    fill: '#000',
}

export const InFrameFontPreset: Partial<ITextProps> = {
    fill: '#0080DB',
}

export const setDefaultFontPreset = (options: Partial<ITextProps>) => {
    merge(DefaultFontPreset, options)
}

export const setInFrameFontPreset = (options: Partial<ITextProps>) => {
    merge(InFrameFontPreset, options)
}
