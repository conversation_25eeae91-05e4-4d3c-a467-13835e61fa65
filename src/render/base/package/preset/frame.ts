import { FramePresetType } from "./frame.type"

const frameMaxWidth = 500
const frameMaxHeight = 500

const frameMinWidth = 64
const frameMinHeight = 64


export const FramePreset: FramePresetType = {
    maxWidth: frameMaxWidth,
    maxHeight: frameMaxHeight,
    minWidth: frameMinWidth,
    minHeight: frameMinHeight,
}

export const setFramePreset = (options: Partial<FramePresetType>) => {
    Object.assign(FramePreset, options)
}
