import { Path, Point } from "fabric"

export function getRectMaxBounds(startPointer: Point, endPointer: Point, maxBoundsNumber: number) {
    const width = Math.abs(startPointer.x - endPointer.x)
    const height = Math.abs(startPointer.y - endPointer.y)
    
    if (width > maxBoundsNumber || height > maxBoundsNumber) {
        const scale = Math.min(maxBoundsNumber / width, maxBoundsNumber / height)
        const newX = startPointer.x + (endPointer.x - startPointer.x) * scale
        const newY = startPointer.y + (endPointer.y - startPointer.y) * scale
        return new Point(newX, newY)
    }
    
    return new Point(endPointer.x, endPointer.y)
}

export function isPathMaxBounds(path: Path, maxBoundsNumber: number) {
    const bound = path.getBoundingRect()
    if (bound.width > maxBoundsNumber || bound.height > maxBoundsNumber) {
        return false
    }
    return true
}

export function isLassoMaxBounds(points: Point[], maxBoundsNumber: number) {
    const minX = Math.min(...points.map(point => point.x))
    const maxX = Math.max(...points.map(point => point.x))
    const minY = Math.min(...points.map(point => point.y))
    const maxY = Math.max(...points.map(point => point.y))
    const width = Math.abs(minX - maxX)
    const height = Math.abs(minY - maxY)
    if (width > maxBoundsNumber || height > maxBoundsNumber) {
        return false
    }
    return true
}
