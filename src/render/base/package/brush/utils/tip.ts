import { FixedLayout, Group, LayoutManager, Textbox } from "fabric"
import { FabricObject } from "fabric"
import { createSvgElement } from "./createSvgElement"
import { TopTips, BottomTips} from './svgpath'
export async function calculateTipPosition(container: Group, target:FabricObject) {
    const containerWidth = container.getScaledWidth()
    const containerHeight = container.getScaledHeight()
    const containerCenter = container.getCenterPoint()
    const containerRelative = {
        left: containerCenter.x - containerWidth / 2,
        top: containerCenter.y - containerHeight / 2,
        width: containerWidth,
        height: containerHeight,
    }
    const targetCenter = target.getCenterPoint()
    const targetWidth = target.getScaledWidth()
    const targetHeight = target.getScaledHeight()
    const targetRelative = {
        left: targetCenter.x - targetWidth / 2,
        top: targetCenter.y - targetHeight / 2,
        width: targetWidth,
        height: targetHeight,
    }
    const index = target.get('index')
    const text = new Textbox(`${index}`, {
        width: 16,
        height: 16,
        left: 19,
        top: 15,
        fontSize: 12,
        fill: '#FFF',
        textAlign: 'center',
        fontWeight: 'bold',
    })
    const topObject = await createSvgElement(TopTips)
    const bottomObject = await createSvgElement(BottomTips)

    const tipTarget = new Group([topObject, bottomObject, text], {
        width: 34,
        height: 16,
        layoutManager: new LayoutManager(new FixedLayout()),
        evented: true,
        subTargetCheck: true,
        selectable: false,
    })
    tipTarget.set({
        isRemoveAction: true,
        removeTarget: target._id_
    })
    const padding = 5
    const tipTargetRect = tipTarget.getBoundingRect()
    let tipTargetX = targetRelative.left
    let tipTargetY = targetRelative.top - tipTarget.height / 2
    const positions = [
        // 左上角
        {
            x: targetRelative.left - padding - 10,
            y: targetRelative.top - tipTarget.height / 2,
            attr: {flipX: false, flipY: false},
            text: { x: -7, y: 0 },
            object: topObject
        },
        // 右上角
        {
            x: targetRelative.left + targetRelative.width + padding + 10,
            y: targetRelative.top - tipTarget.height / 2,
            attr: {flipX: true, flipY: false},
            text: { x: 7, y: 0 },
            object: topObject
        },
        // 左下角
        {
            x: targetRelative.left - padding - 10,
            y: targetRelative.top + targetRelative.height + padding + 2,
            attr: {flipX: false, flipY: false},
            text: { x: -7, y: 0 },
            object: bottomObject
        },
        // 右下角
        {
            x: targetRelative.left + targetRelative.width + padding + 10,
            y: targetRelative.top + targetRelative.height + padding + 2,
            attr: {flipX: true, flipY: false},
            text: { x: 7, y: 0 },
            object: bottomObject
        },
    ]

    const doesOverflowContainer = (left: number, top: number) => {
        return left - tipTarget.width / 2 < containerRelative.left ||
            left + tipTarget.width / 2 > containerRelative.left + containerRelative.width ||
            top - tipTarget.height / 2 < containerRelative.top ||
            top + tipTarget.height / 2 > containerRelative.top + containerRelative.height
    }

    for (const position of positions) {
        if (!doesOverflowContainer(position.x, position.y)) {
            tipTargetX = position.x
            tipTargetY = position.y
            tipTarget.add(position.object, text)
            if (position.object === topObject) {
                bottomObject.set({
                    visible: false
                })
            } else {
                topObject.set({
                    visible: false
                })
            }
            position.object.set({
                ...position.attr,
            })
            text.set({
                left: position.text.x,
                top: position.text.y,
            })
            break
        }
    }
    tipTarget.set({ left: tipTargetX, top: tipTargetY })
    container.add(tipTarget)
    container.canvas?.renderAll()
    return tipTarget
}
    