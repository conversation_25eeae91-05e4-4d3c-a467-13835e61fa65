import { FabricObject, Group, Point, Path, Polygon } from "fabric";

export function inContainerByPoint(container: Group, point: Point) {
    const containerRect = container.getBoundingRect()
    const left = containerRect.left
    const top = containerRect.top
    const right = left + containerRect.width
    const bottom = top + containerRect.height
    return point.x > left &&
            point.x < right &&
            point.y > top &&
            point.y < bottom
}

export function isCompletelyOutside(target: FabricObject, container: Group) {
    if (target instanceof Path) {
        // 对于 Path 对象，检查所有路径点
        const path = target.path;
        if (!path || path.length === 0) return true;
        
        // 检查所有点是否都在容器外
        return path.every((command: any) => {
            if (Array.isArray(command) && command.length >= 2) {
                return !inContainerByPoint(container, new Point(command[1], command[2]));
            }
            return true;
        });
    } else if (target instanceof Polygon) {
        // 对于多边形对象，检查所有顶点
        const points = target.points;
        if (!points || points.length === 0) return true;
        
        // 检查所有点是否都在容器外
        return points.every((point: { x: number; y: number }) => {
            return !inContainerByPoint(container, new Point(point.x, point.y));
        });
    } else {
        // 对于其他对象（如矩形），检查边界矩形的角点
        const targetRect = target.getBoundingRect();
        const left = targetRect.left;
        const top = targetRect.top;
        const right = left + targetRect.width;
        const bottom = top + targetRect.height;
        
        return !inContainerByPoint(container, new Point(left, top)) &&
               !inContainerByPoint(container, new Point(right, top)) &&
               !inContainerByPoint(container, new Point(left, bottom)) &&
               !inContainerByPoint(container, new Point(right, bottom));
    }
}