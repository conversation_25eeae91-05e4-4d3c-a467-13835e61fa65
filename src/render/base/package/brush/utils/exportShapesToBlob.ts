import { Group } from "fabric";
import { ExportOptions } from "../types";
import Logger from "../../../../logger";
import { transformShapeToBlob } from "../../../../utils/canvas";
import { clonePropertiesToInclude } from "../../../ownDefaults/clone";
import { SmartMask } from "../../mask";
export async function exportShapesToBlob(targetGroup: Group, opt: ExportOptions) {
  if (!targetGroup) {
    Logger.error(`导出容器${opt.exportContainerType}不存在`);
    return;
  }
  const fakeTargetGroup = await targetGroup.clone(clonePropertiesToInclude)
  fakeTargetGroup.set('opacity', 1)
  fakeTargetGroup.getObjects().forEach(shape => {
    shape.set('opacity', 1)
  })
  
  // 设置背景色和形状填充色
  if (opt.backgroundFill) {
    fakeTargetGroup.set('backgroundColor', opt.backgroundFill)
  }    
  if (opt.shapeFill) {
      fakeTargetGroup.getObjects().forEach(shape => {
        if (shape.get('isMask')) {
          if (shape.type === 'path') {
            shape.set('stroke', opt.shapeFill)
          } else if (shape instanceof SmartMask) {
            shape.replacePattern()
          } else {
            shape.set('fill', opt.shapeFill)
          }
        }
      })
    }

  const psr: Promise<Blob>[] = []
  if (opt.isMerge) {
    psr.push(transformShapeToBlob(fakeTargetGroup, opt.ext))
  } else {
    const objects = fakeTargetGroup.getObjects();
    const targetObjects = objects.filter(item => item.get('isMask'))
    const ps: Promise<Blob>[] = targetObjects.map((item) => {
      return new Promise((resolve, reject) => {
        targetObjects.forEach(obj => {
          obj.set('visible', false)
        })
        item.set('visible', true)
        fakeTargetGroup.clone(clonePropertiesToInclude).then(target => {
          resolve(transformShapeToBlob(target, opt.ext))
        }).catch(reject)
      });
    });
    psr.push(...ps)
  }
  return Promise.all(psr)
}