import { ElementName } from "../../../../utils"
import { Canvas, FabricObject } from "fabric"

export function triggerEvent(this: <PERSON>vas, target: FabricObject, element: FabricObject) {
    switch (target._name_) {
        case ElementName.MASK_RECT:
            this.fire('mask:rect:created', { target: target, element: element })
            break
        case ElementName.MASK_POLYGON:
            this.fire('mask:lasso:created', { target: target, element: element })
            break
        case ElementName.MASK_PATH:
            this.fire('mask:path:created', { target: target, element: element })
            break
    }
}