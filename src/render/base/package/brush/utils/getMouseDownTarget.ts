import { Canvas, Group, TEvent } from "fabric";

export const MASK_CONTAINER_NAME = "titleAndButtonContainer"

export function getMouseDownTarget(canvas: Canvas, e: TEvent) {
    const target = canvas.findTarget(e.e)
    if (!target) return
    if (target.get('maskContainerName') === MASK_CONTAINER_NAME) {
        const objects = (target as Group).getObjects()
        const pointer = canvas.getScenePoint(e.e);
        const targetObject = objects.filter(obj => obj.containsPoint(pointer))
        if (targetObject.length > 0) {
            return targetObject[0]
        }
    }
    return
}