import { <PERSON>vas, PencilBrush, Point, Polygon, TEvent } from "fabric";
import { LassoBrushOptions } from "./type";
import { IBaseBrush } from "../base/baseBrush";
import { isLassoMaxBounds } from "../utils/maxBounds";
import { nanoid } from "nanoid";
import { ElementName } from "../../../../utils";
import { getMouseDownTarget } from "../utils/getMouseDownTarget";
import { isCompletelyOutside } from "../utils/overflow";
export class LassoBrush extends IBaseBrush {
  _isDrawing: boolean = false
  _pathLength: number = 0
  constructor(canvas: Canvas, public options: LassoBrushOptions) {
    super(canvas, options)
    this.decimate = 10
  }


  onMouseDown(pointer: Point, e: TEvent): void {
    const target = getMouseDownTarget(this.canvas, e)
    if (target) {
        this.triggerRemoveTip(target)
        this.canvas.fire('mask:lasso:deleted', {
            target,
            element: this.options.targetElement
        })
        return
    }
    if (this.validMaskCount()) return;
    if (!this.canvas._isMainEvent(e.e)) return;
    this._isDrawing = true;

    super.onMouseDown(pointer, { e: e.e });
  }

  onMouseMove(pointer: Point): void {
    if (!this._isDrawing) return;
    if (this.options.maxBoundsNumber && !(isLassoMaxBounds([...this['_points'], pointer], this.options.maxBoundsNumber))) return
    this._addPoint(pointer)
    this._render()
  }

  onMouseUp({ e }: TEvent): boolean {
    if (!this.canvas._isMainEvent(e)) return true;
    if (!this._isDrawing) return false;
    this._isDrawing = false;
    this.drawStraightLine = false;
    this['oldEnd'] = undefined;
    this._finalizeAndAddPath();
    return false;
  }

  _addPoint = (point: Point) => {
    // 计算与上一个点的距离
    const points = this['_points']
    if (points.length > 0) {
      const lastPoint = points[points.length - 1];
      const dx = point.x - lastPoint.x;
      const dy = point.y - lastPoint.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      this._pathLength += distance;  // 累加路径长度

      this.canvas.contextTop.lineDashOffset = -this._pathLength;
    }

    // 原有的重复点检查逻辑
    if (
      points.length > 1 &&
      point.eq(points[points.length - 1])
    ) {
      return false;
    }

    // 直线模式处理
    if (this.drawStraightLine && points.length > 1) {
      this['_hasStraightLine'] = true;
      points.pop();
    }

    points.push(point);
    return true;
  }

  _render(ctx: CanvasRenderingContext2D = this.canvas.contextTop) {
    const points = this['_points']
    let p1 = points[0],
      p2 = points[1];
    this._saveAndTransform(ctx);
    ctx.beginPath();
    if (points.length === 2 && p1.x === p2.x && p1.y === p2.y) {
      const width = this.width / 1000;
      p1.x -= width;
      p2.x += width;
    }
    ctx.moveTo(p1.x, p1.y);
    ctx.lineWidth = 2;
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'

    // 绘制黑色虚线
    ctx.setLineDash([5, 5]);
    ctx.strokeStyle = 'black';
    for (let i = 1; i < points.length; i++) {
      PencilBrush.drawSegment(ctx, p1, p2);
      p1 = points[i];
      p2 = points[i + 1];
    }
    ctx.lineTo(p1.x, p1.y);
    ctx.stroke();

    // 绘制白色虚线
    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);
    p1 = points[0];
    p2 = points[1];
    ctx.setLineDash([5, 5]);
    ctx.lineDashOffset = 5;
    ctx.strokeStyle = 'white';
    for (let i = 1; i < points.length; i++) {
      PencilBrush.drawSegment(ctx, p1, p2);
      p1 = points[i];
      p2 = points[i + 1];
    }
    ctx.lineTo(p1.x, p1.y);
    ctx.stroke();

    ctx.restore();
  }

  _finalizeAndAddPath() {
    const ctx = this.canvas.contextTop;
    ctx.closePath();
    if (this.decimate) {
      this['_points'] = this.decimatePoints(this['_points'], this.decimate);
    }
    const _points = this['_points']
    const shape = new Polygon(_points, {
      fill: this.options.shapeColor,
      strokeLineCap: 'round',
      strokeLineJoin: 'round',
    })
    shape.set({
      _id_: nanoid(),
      _name_: ElementName.MASK_POLYGON,
      _parent_id_: this.maskContainer._id_,
      _parent_name_: this.maskContainer._name_,
      _old_prompt_: '',
      _new_prompt_: '',
      _message_id_: '',
      showTitle: this.options.showShapeTitle,
      showCloseBtn: this.options.showShapeCloseBtn,
      erase: this.options.erase,
      globalCompositeOperation: this.options.erase ? 'destination-out' : 'source-over',
      _loading_element_: false,
      _custom_data_history_: {},
      _custom_data_: {},
      isMask: true,
      maskType: 'polygon',
      index: this.getMaskCount() + 1
    })
    if (isCompletelyOutside(shape, this.maskContainer)) {
      this.canvas.requestRenderAll()
      this.canvas.clearContext(ctx)
      return
    }
    this.canvas.fire('mask:lasso:before', {
      target: shape,
      element: this.options.targetElement
    })
    this.maskContainer.add(shape)
    this.canvas.requestRenderAll()
    this.canvas.clearContext(ctx)
    this.canvas.fire('mask:lasso:created', {
      target: shape,
      element: this.options.targetElement
    })
    this.renderTip()
  }
}