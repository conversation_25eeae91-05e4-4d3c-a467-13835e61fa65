import {Canvas, FabricObject, Rect } from "fabric";
import { PencilBrush } from "fabric";
import { BaseBrushOptions, ExportOptions } from "../types";
import { createMaskContainer, createTitleAndButtonContainer } from "../utils";
import { Group } from "fabric";
import { exportShapesToBlob } from "../utils/exportShapesToBlob";
import { calculateTipPosition } from "../utils/tip";
import {ElementName, getViewPortVisibleArea, WarningType} from "../../../../utils";
import Logger from "../../../../logger";
import { triggerEvent } from "../utils/event";
import { SmartMask } from "../../mask";
import {Container} from "../../container/container";

export abstract class IBaseBrush extends PencilBrush {
    // 承载图形容器
  public maskContainer: Group

  public titleAndButtonContainer: Group

  public stashShape: FabricObject[] = []

  public tipsEl: Group[] = []

  private maskElement: Rect | undefined;
  private targetElementParent: Canvas | Container;
  private targetElementParentZIndex: number;



  constructor(canvas: Canvas, public options: BaseBrushOptions) {
    super(canvas);
    this.maskContainer = createMaskContainer.call(this.options.targetElement, {
        name: this.options.shapeContainerName,
        opacity: this.options.shapeContainerOpacity,
    })
    this.titleAndButtonContainer = createTitleAndButtonContainer.call(this.options.targetElement, {
        name: 'titleAndButtonContainer',
        opacity: this.options.shapeContainerOpacity,
        showTitle: this.options.showShapeTitle,
    })
    this.options.targetElement.bringObjectToFront(this.titleAndButtonContainer)
    this.targetElementParent = (this.options.targetElement.parent ?? this.canvas ) as Container || this.canvas;
    this.targetElementParentZIndex = this.targetElementParent._objects.findIndex(item => item === options.targetElement);
    this.options.targetElement.set("selectable", false);
    this.canvas.set('selection', false)
    this.canvas.discardActiveObject();
    if (options.isMask) {
      this._renderMask()
    }
    this.canvas.bringObjectToFront(this.options.targetElement)
    this.canvas.set('isDisabledContextMenu', true)
    this.bindEvent()
    // this.changeTargetStatus(true)
    this.canvas.renderAll()
  }


  _renderMask() {
    const viewportArea = getViewPortVisibleArea(this.canvas);
    const width = viewportArea.right - viewportArea.left;
    const height = viewportArea.bottom - viewportArea.top;
    this.maskElement = new Rect({
      width,
      height,
      scaleX: 1,
      scaleY: 1,
      left: viewportArea.left + width / 2,
      top: viewportArea.top + height / 2,
      fill: 'rgba(0, 0, 0, 0.5)',
      selectable: false,
      evented: false,
    });
    this.canvas.add(this.maskElement);
    this.bindEvent();
  }

  bindEvent = () => {
    this.canvas.on('object:added', this.objectAddedHandler)
    if (this.options.isMask) {
      this.canvas.on('viewport:translate', this.viewportTranslateHandler);
      this.canvas.on('viewport:zoom', this.viewportZoomHandler);
    }
  }

  unbindEvent = () => {
    this.canvas.off('object:added', this.objectAddedHandler)
    if (this.options.isMask) {
      this.canvas.off('viewport:translate', this.viewportTranslateHandler);
      this.canvas.off('viewport:zoom', this.viewportZoomHandler);
    }
  }

  objectAddedHandler = () => {
    if (this.options.isMask && this.maskElement) {
      this.canvas.bringObjectToFront(this.maskElement)
      this.canvas.bringObjectToFront(this.options.targetElement)
    }
  }

  private viewportTranslateHandler = () => {
    if (!this.maskElement) return
    // 保持遮罩层固定在画布中心，不随视口平移
    const viewportArea = getViewPortVisibleArea(this.canvas);
    const width = viewportArea.right - viewportArea.left;
    const height = viewportArea.bottom - viewportArea.top;
    this.maskElement.set({
      width,
      height,
      left: viewportArea.left + width / 2,
      top: viewportArea.top + height / 2,
    });
    this.canvas.renderAll();
  }

  private viewportZoomHandler = () => {
    if (!this.maskElement) return
    // 保持遮罩层缩放比例与视口一致
    const viewportArea = getViewPortVisibleArea(this.canvas);
    const width = viewportArea.right - viewportArea.left;
    const height = viewportArea.bottom - viewportArea.top;
    this.maskElement.set({
      width,
      height,
      left: viewportArea.left + width / 2,
      top: viewportArea.top + height / 2,
    });
    this.canvas.renderAll();
  }


  getMasks = () => {
    return this.maskContainer.getObjects().filter(el => el.get('isMask'))
  }



  getMaskCount = () => {
    return this.maskContainer.getObjects().filter(el => el.get('isMask')).length
  }

  /**
   * 设置targetElement的subTargetCheck状态
   * @param subTargetCheck
   */
  private changeTargetStatus = (subTargetCheck: boolean) => {
    this.options.targetElement.subTargetCheck = subTargetCheck
  }

  /**
   * 校验mask数量是否超出限制
   * @returns
   */
  validMaskCount = () => {
    if (!this.options.maxMaskCount) return false
    const result = this.getMaskCount() >= this.options.maxMaskCount
    if (result) {
      this.canvas.fire('warning', {
        objects: this.maskContainer.getObjects(),
        target: this.options.targetElement,
        type: WarningType.MASK_MAX_COUNT,
      })
      Logger.warn(`mask数量超过限制，最大数量为${this.options.maxMaskCount}`)
    }
    return result
  }

  /**
   * 清除所有mask
   * TODO：需要记录历史
   */
  clearTargetElement = () => {
    const els = this.maskContainer.getObjects().filter(el => el.get('isMask'))
    els.forEach(el => {
      switch(el._name_) {
        case ElementName.MASK_RECT:
          el.parent?.remove(el)
          this.canvas.fire('mask:rect:deleted', { target: el, element: this.options.targetElement })
          break
        case ElementName.MASK_PATH:
          el.parent?.remove(el)
          this.canvas.fire('mask:path:deleted', { target: el, element: this.options.targetElement })
          break
        case ElementName.MASK_POLYGON:
          el.parent?.remove(el)
          this.canvas.fire('mask:lasso:deleted', { target: el, element: this.options.targetElement })
          break
        case ElementName.MASK_SMART:
          el.parent?.remove(el)
          this.canvas.fire('mask:smart:deleted', { target: el, element: this.options.targetElement })
          break
      }
    })
    this.removeTip()
    this.canvas.renderAll()
  }


  /**
   * 导出容器到blob
   * @param options
   * @returns
   */
  exportShapesToBlob = (options: ExportOptions) => {
    return exportShapesToBlob(this.maskContainer, options)
  }

  /**
   * 设置显示或隐藏指定id的shape
   * @param id
   * @param visible
   */
  setShowShape = (id: string, visible: boolean) => {
    if (visible) {
      const target = this.stashShape.find(obj => obj._id_ === id)
      if (target) {
        this.maskContainer.add(target)
        return target
      }
    } else {
      const target = this.maskContainer
      .getObjects()
      .find(obj => obj._id_ === id)
      if (target) {
        this.maskContainer.remove(target)
        this.stashShape.push(target)
        return target
      }
    }
  }

  /**
   * 导出容器到canvas ImageData
   * @returns
   */
  exportContainerToCanvas = (): ImageData | null => {
    const canvas = this.maskContainer.toCanvasElement()
    const ctx = canvas.getContext('2d')
    if (!ctx) return null
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    return imageData
  }
  /**
   * 设置擦除模式
   * @param isErase
   */
  setErase = (isErase: boolean) => {
    this.options.erase = isErase
  }
  /**
   * 添加智能mask
   * @param mask
   */
  addSmartMask = (mask: SmartMask) => {
    mask.set({
      _parent_id_: this.maskContainer._id_,
      _parent_name_: this.maskContainer._name_,
      _old_prompt_: '',
      _new_prompt_: '',
      _message_id_: '',
      _loading_element_: false,
      _custom_data_history_: {},
      _custom_data_: {},
      index: this.getMaskCount() + 1,
      showTitle: this.options.showShapeTitle,
      showCloseBtn: this.options.showShapeCloseBtn,
      erase: this.options.erase,
      isMask: true,
      maskType: 'smart'
    })
    this.maskContainer.add(mask)
    this.canvas.renderAll()
    this.canvas.fire('mask:smart:created', { target: mask, element: this.options.targetElement })
  }

  removeTip = () => {
    const tips = this.maskContainer.getObjects().filter(el => el.get('isMask'))
    this.titleAndButtonContainer.getObjects().forEach(el => {
        el.parent?.remove(el)
    })
    return tips
  }


  renderTip = () => {
    if (!this.options.showShapeTitle) return
    const tips = this.removeTip()
    Promise.all(tips.map(tip => {
      return calculateTipPosition(this.titleAndButtonContainer, tip)
    })).then(tips => {
      this.tipsEl = tips
      this.canvas.renderAll()
    })
  }

  triggerRemoveTip = (target: FabricObject) => {
    const isRemoveAction = target.get('isRemoveAction')
    const removeTarget = target.get('removeTarget')
    if (isRemoveAction && removeTarget) {
      const tip = this.tipsEl.find(el => el === target)
      tip?.parent?.remove(tip)
      this.setShowShape(removeTarget, false)
      triggerEvent.call(this.canvas, target, this.options.targetElement)
      this.maskContainer.getObjects().forEach((el, index) => {
        if (el.get('isMask')) {
          el.set({
            index: index + 1
          })
        }
      })
      this.renderTip()
      this.canvas.renderAll()
    }
  }



  destroy = () => {
    this.options.targetElement.set("selectable", true);
    this.canvas.set('selection', true)
    this.canvas.set('isDisabledContextMenu', false)
    this.maskContainer.set({
      visible: false,
      selectable: false,
      evented: false,
      subTargetCheck: false,
    })
    this.titleAndButtonContainer.set({
      visible: false,
      selectable: false,
      evented: false,
      subTargetCheck: false,
    })
    this.unbindEvent()
    this.changeTargetStatus(false)
    if (this.options.isMask && this.maskElement) {
      this.canvas.remove(this.maskElement)
      this.maskElement = undefined
    }

    if (!this.options.isProtected) {
      this.titleAndButtonContainer.parent?.remove(this.titleAndButtonContainer)
    }
    this.canvas.remove(this.options.targetElement)
    this.targetElementParent.insertAt(this.targetElementParentZIndex, this.options.targetElement)
    this.canvas.renderAll()
  }
}

