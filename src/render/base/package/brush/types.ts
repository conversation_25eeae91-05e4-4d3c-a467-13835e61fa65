import { IImage } from "../image/image";
import { IBaseBrush } from "./base/baseBrush";

export interface BaseBrushOptions {
  shapeColor: string; // 图形渲染颜色
  shapeContainerName: string; // 承载图形容器名称
  shapeContainerOpacity: number; // 承载图形容器透明度
  showShapeTitle: boolean; // 是否显示图形标题
  showShapeCloseBtn: boolean; // 是否显示图形删除按钮
  targetElement: IImage; // 承载图形元素
  erase: boolean; // 是否擦除
  isMask: boolean;// 是否遮罩
  maxBoundsNumber?: number; // 最大承载图形宽高
  maxMaskCount?: number; // 最大承载图形数量
  getTitle?: (ins: IBaseBrush) => string; // 获取图形标题
  isProtected?: boolean; // 是否保护图形
}

export type ExportOptions = {
  isMerge: boolean // 是否合并为一张图片
  ext: string // 图片格式
  shapeFill?: string // 形状填充颜色
  backgroundFill?: string // 背景填充颜色
  exportContainerType: string // 导出容器类型
 }
