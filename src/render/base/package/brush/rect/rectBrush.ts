import { IBaseBrush } from "../base/baseBrush";
import { Canvas, Point, Rect, TEvent } from "fabric";
import { BaseBrushOptions } from "../types";
import { getRectMaxBounds } from "../utils/maxBounds";
import { nanoid } from "nanoid";
import { ElementName } from "../../../../utils";
import { getMouseDownTarget } from "../utils/getMouseDownTarget";
import { isCompletelyOutside } from "../utils/overflow";
export class RectBrush extends IBaseBrush {
    startPointer: Point | null = null
    endPointer: Point | null = null
    _isDrawing: boolean = false
    constructor(canvas: Canvas, options: BaseBrushOptions) {
        super(canvas, options)
    }

    onMouseDown(pointer: Point, e: TEvent): void {
        const target = getMouseDownTarget(this.canvas, e)
        if (target) {
            this.triggerRemoveTip(target)
            this.canvas.fire('mask:rect:deleted', {
                target,
                element: this.options.targetElement
            })
            return
        }
        if (this.validMaskCount()) return;
        this._isDrawing = true;
        this.startPointer = pointer
    }

    onMouseMove(pointer: Point): void {
        if (!this._isDrawing) return;
        if (!this.startPointer) return
        if (this.options.maxBoundsNumber) {
            this.endPointer = getRectMaxBounds(this.startPointer, pointer, this.options.maxBoundsNumber)
        } else {
            this.endPointer = pointer
        }
        this._render()
    }

    onMouseUp({ e }: TEvent): boolean {
        if (!this.canvas._isMainEvent(e)) return true;
        if (!this._isDrawing) return false;
        this._isDrawing = false;
        this.drawStraightLine = false;
        this['oldEnd'] = undefined;
        this._finalizeAndAddPath();
        return false
    }

    _render(ctx: CanvasRenderingContext2D = this.canvas.contextTop) {
        const viewportTransform = this.canvas.viewportTransform
        if (!this.startPointer || !this.endPointer) return
        const fakeStartPointer = new Point({
            x: this.startPointer.x,
            y: this.startPointer.y
        }).transform(viewportTransform)
        const fakeEndPointer = new Point({
            x: this.endPointer.x,
            y: this.endPointer.y
        }).transform(viewportTransform)
        const width = fakeEndPointer.x - fakeStartPointer.x
        const height = fakeEndPointer.y - fakeStartPointer.y
        const left = width < 0 ? fakeStartPointer.x + width : fakeStartPointer.x
        const top = height < 0 ? fakeStartPointer.y + height : fakeStartPointer.y
        this.canvas.clearContext(ctx)
        ctx.save()
        ctx.beginPath()
        ctx.fillStyle = this.color;
        ctx.setLineDash([5, 5]);
        ctx.strokeStyle = 'black';
        ctx.rect(left, top, Math.abs(width), Math.abs(height));
        ctx.lineWidth = 2;
        ctx.stroke();
        ctx.strokeStyle = 'white';
        ctx.lineDashOffset = 5;
        ctx.stroke();
        ctx.restore()
    }

    _finalizeAndAddPath() {
        const ctx = this.canvas.contextTop;
        ctx.closePath();

        if (this.decimate) {
        this['_points'] = this.decimatePoints(this['_points'], this.decimate);
        }
        
        if (!this.startPointer || !this.endPointer) return
        const width = ((this.endPointer?.x || 0) - (this.startPointer?.x || 0))
        const height = ((this.endPointer?.y || 0) - (this.startPointer?.y || 0))
        const centerX = (this.startPointer?.x || 0) + (width) / 2
        const centerY = (this.startPointer?.y || 0) + (height) / 2
        const target = new Rect({
            left: centerX,
            top: centerY,
            width,
            height,
            globalCompositeOperation: this.options.erase ? 'destination-out' : 'source-over',
            fill: this.options.shapeColor,
          })
          target.set({
            _id_: nanoid(),
            _name_: ElementName.MASK_RECT,
            _parent_id_: this.maskContainer._id_,
            _parent_name_: this.maskContainer._name_,
            _old_prompt_: '',
            _new_prompt_: '',
            _message_id_: '',
            _loading_element_: false,
            _custom_data_history_: {},
            _custom_data_: {},
            index: this.getMaskCount() + 1,
            showTitle: this.options.showShapeTitle,
            showCloseBtn: this.options.showShapeCloseBtn,
            erase: this.options.erase,
            isMask: true,
            maskType: 'rect'
          })
          if (isCompletelyOutside(target, this.maskContainer)) {
            this.startPointer = null
            this.endPointer = null
            this.canvas.requestRenderAll()
            this.canvas.clearContext(ctx)
            return
          }
          this.canvas.fire('mask:rect:before', {
            target,
            element: this.options.targetElement
          })
          this.maskContainer.add(target)
          this.startPointer = null
          this.endPointer = null
          this.canvas.requestRenderAll()
          this.canvas.clearContext(ctx)
          this.canvas.fire('mask:rect:created', {
            target: target,
            element: this.options.targetElement
          })
        this.renderTip()
    }
}