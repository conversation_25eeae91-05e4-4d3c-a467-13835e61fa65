import { FabricObject } from "fabric";
import { Render } from "../render";

export class Override {
    private _originalOnObjectRemoved: (obj: FabricObject) => void;
  constructor(private _render: Render) {
    this._originalOnObjectRemoved = this._render._FC._onObjectRemoved;
    this.setObjectRemovedByCustom();
  }
  public setObjectRemovedByCustom() {
    this._render._FC._onObjectRemoved = this._onObjectRemoved;
  }
  public setObjectRemovedByOriginal() {
    this._render._FC._onObjectRemoved = this._originalOnObjectRemoved;
  }
  /**
   * 移除对象时触发
   * @param obj 
   */
  private _onObjectRemoved = (obj: FabricObject) => {
    this._render._FC._objectsToRender = undefined;
    if (obj === this._render._FC._hoveredTarget) {
      this._render._FC._hoveredTarget = undefined;
      this._render._FC._hoveredTargets = [];
    }
    obj._set('canvas', undefined);
    this._render._FC.fire('object:removed', { target: obj });
    obj.fire('removed', { target: this._render._FC });
  }

  public _destroy() {
    this._render._FC._onObjectRemoved = this._originalOnObjectRemoved;
  }
}
