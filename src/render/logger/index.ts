import { <PERSON><PERSON> } from "../render";
import { log } from "./log";
export default class Logger {
  constructor(private render: Render) {
    this.render = render;
  }
  
  info = (message: string, ...args: any[]) => {
    if (this.render.debug) {
      log(message, 'info', ...args);
    }
  }

  warn = (message: string, ...args: any[]) => {
    if (this.render.debug) {
      log(message, 'warning', ...args);
    }
  }

  error = (message: string, ...args: any[]) => {
    if (this.render.debug) {
      log(message, 'error', ...args);
    }
  }

  static info = (message: string, ...args: any[]) => {
    if (localStorage.getItem('whee-infinite-canvas:debug')) {
      log(message, 'info', ...args);
    }
  }

  static warn = (message: string, ...args: any[]) => {
    if (localStorage.getItem('whee-infinite-canvas:debug')) {
      log(message, 'warning', ...args);
    }
  }

  static error = (message: string, ...args: any[]) => {
    if (localStorage.getItem('whee-infinite-canvas:debug')) {
      log(message, 'error', ...args);
    }
  }
  
  
}
